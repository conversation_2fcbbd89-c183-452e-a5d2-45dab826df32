{% extends "base.html" %}

{% block title %}Gallery - S.N. Pvt. Industrial Training Institute{% endblock %}

{% block content %}
<!-- Page Header -->
<section class="bg-gradient-to-r from-primary-700 to-primary-500 text-white py-16">
    <div class="container mx-auto px-4">
        <div class="text-center">
            <h1 class="text-4xl lg:text-5xl font-bold mb-4">Gallery</h1>
            <p class="text-xl text-primary-100 mb-6">Explore our institute facilities and activities</p>
            <nav class="text-primary-200">
                <a href="/" class="hover:text-white transition-colors">Home</a>
                <span class="mx-2">/</span>
                <span>Gallery</span>
            </nav>
        </div>
    </div>
</section>

<!-- Gallery Filters -->
<section class="py-8 bg-white border-b">
    <div class="container mx-auto px-4">
        <div class="flex flex-wrap justify-center gap-4">
            <button class="gallery-filter active px-6 py-2 rounded-full bg-primary-600 text-white hover:bg-primary-700 transition-colors" data-filter="all">
                All Photos
            </button>
            <button class="gallery-filter px-6 py-2 rounded-full bg-gray-200 text-gray-700 hover:bg-gray-300 transition-colors" data-filter="infrastructure">
                Infrastructure
            </button>
            <button class="gallery-filter px-6 py-2 rounded-full bg-gray-200 text-gray-700 hover:bg-gray-300 transition-colors" data-filter="workshops">
                Workshops
            </button>
            <button class="gallery-filter px-6 py-2 rounded-full bg-gray-200 text-gray-700 hover:bg-gray-300 transition-colors" data-filter="activities">
                Activities
            </button>
            <button class="gallery-filter px-6 py-2 rounded-full bg-gray-200 text-gray-700 hover:bg-gray-300 transition-colors" data-filter="events">
                Events
            </button>
        </div>
    </div>
</section>

<!-- Gallery Grid -->
<section class="py-16 bg-gray-50">
    <div class="container mx-auto px-4">
        <div class="gallery-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            <!-- Infrastructure Images -->
            <div class="gallery-item infrastructure" data-category="infrastructure">
                <img src="/assets/images/01.jpg" alt="Institute Building" class="w-full h-64 object-cover rounded-lg shadow-lg hover:shadow-xl transition-shadow cursor-pointer no-hover" onclick="openLightbox(this)">
                <div class="gallery-overlay">
                    <div class="text-white">
                        <h3 class="font-semibold">Institute Building</h3>
                        <p class="text-sm opacity-90">Main campus building</p>
                    </div>
                </div>
            </div>

            <div class="gallery-item infrastructure" data-category="infrastructure">
                <img src="/assets/images/02.jpg" alt="Workshop Area" class="w-full h-64 object-cover rounded-lg shadow-lg hover:shadow-xl transition-shadow cursor-pointer no-hover" onclick="openLightbox(this)">
                <div class="gallery-overlay">
                    <div class="text-white">
                        <h3 class="font-semibold">Workshop Area</h3>
                        <p class="text-sm opacity-90">Practical training facility</p>
                    </div>
                </div>
            </div>

            <div class="gallery-item infrastructure" data-category="infrastructure">
                <img src="/assets/images/03.jpg" alt="Classroom" class="w-full h-64 object-cover rounded-lg shadow-lg hover:shadow-xl transition-shadow cursor-pointer no-hover" onclick="openLightbox(this)">
                <div class="gallery-overlay">
                    <div class="text-white">
                        <h3 class="font-semibold">Classroom</h3>
                        <p class="text-sm opacity-90">Modern learning environment</p>
                    </div>
                </div>
            </div>

            <div class="gallery-item infrastructure" data-category="infrastructure">
                <img src="/assets/images/04.jpg" alt="Laboratory" class="w-full h-64 object-cover rounded-lg shadow-lg hover:shadow-xl transition-shadow cursor-pointer no-hover" onclick="openLightbox(this)">
                <div class="gallery-overlay">
                    <div class="text-white">
                        <h3 class="font-semibold">Laboratory</h3>
                        <p class="text-sm opacity-90">Well-equipped lab facilities</p>
                    </div>
                </div>
            </div>

            <!-- Workshop Images -->
            <div class="gallery-item workshops" data-category="workshops">
                <img src="/assets/images/1.jpg" alt="Electrical Workshop" class="w-full h-64 object-cover rounded-lg shadow-lg hover:shadow-xl transition-shadow cursor-pointer no-hover" onclick="openLightbox(this)">
                <div class="gallery-overlay">
                    <div class="text-white">
                        <h3 class="font-semibold">Electrical Workshop</h3>
                        <p class="text-sm opacity-90">Hands-on electrical training</p>
                    </div>
                </div>
            </div>

            <div class="gallery-item workshops" data-category="workshops">
                <img src="/assets/images/2.jpg" alt="Training Session" class="w-full h-64 object-cover rounded-lg shadow-lg hover:shadow-xl transition-shadow cursor-pointer no-hover" onclick="openLightbox(this)">
                <div class="gallery-overlay">
                    <div class="text-white">
                        <h3 class="font-semibold">Training Session</h3>
                        <p class="text-sm opacity-90">Practical skill development</p>
                    </div>
                </div>
            </div>

            <div class="gallery-item workshops" data-category="workshops">
                <img src="/assets/images/3.jpg" alt="Workshop Equipment" class="w-full h-64 object-cover rounded-lg shadow-lg hover:shadow-xl transition-shadow cursor-pointer no-hover" onclick="openLightbox(this)">
                <div class="gallery-overlay">
                    <div class="text-white">
                        <h3 class="font-semibold">Workshop Equipment</h3>
                        <p class="text-sm opacity-90">Modern training tools</p>
                    </div>
                </div>
            </div>

            <!-- Activities Images -->
            <div class="gallery-item activities" data-category="activities">
                <img src="/assets/images/4.jpg" alt="Student Activities" class="w-full h-64 object-cover rounded-lg shadow-lg hover:shadow-xl transition-shadow cursor-pointer no-hover" onclick="openLightbox(this)">
                <div class="gallery-overlay">
                    <div class="text-white">
                        <h3 class="font-semibold">Student Activities</h3>
                        <p class="text-sm opacity-90">Extracurricular programs</p>
                    </div>
                </div>
            </div>

            <div class="gallery-item activities" data-category="activities">
                <img src="/assets/images/5.jpg" alt="Sports Activities" class="w-full h-64 object-cover rounded-lg shadow-lg hover:shadow-xl transition-shadow cursor-pointer no-hover" onclick="openLightbox(this)">
                <div class="gallery-overlay">
                    <div class="text-white">
                        <h3 class="font-semibold">Sports Activities</h3>
                        <p class="text-sm opacity-90">Physical fitness programs</p>
                    </div>
                </div>
            </div>

            <!-- Events Images -->
            <div class="gallery-item events" data-category="events">
                <img src="/assets/images/6.jpg" alt="Annual Function" class="w-full h-64 object-cover rounded-lg shadow-lg hover:shadow-xl transition-shadow cursor-pointer no-hover" onclick="openLightbox(this)">
                <div class="gallery-overlay">
                    <div class="text-white">
                        <h3 class="font-semibold">Annual Function</h3>
                        <p class="text-sm opacity-90">Celebration events</p>
                    </div>
                </div>
            </div>

            <div class="gallery-item events" data-category="events">
                <img src="/assets/images/7.jpg" alt="Graduation Ceremony" class="w-full h-64 object-cover rounded-lg shadow-lg hover:shadow-xl transition-shadow cursor-pointer no-hover" onclick="openLightbox(this)">
                <div class="gallery-overlay">
                    <div class="text-white">
                        <h3 class="font-semibold">Graduation Ceremony</h3>
                        <p class="text-sm opacity-90">Achievement recognition</p>
                    </div>
                </div>
            </div>

            <div class="gallery-item events" data-category="events">
                <img src="/assets/images/8.JPG" alt="Industry Visit" class="w-full h-64 object-cover rounded-lg shadow-lg hover:shadow-xl transition-shadow cursor-pointer no-hover" onclick="openLightbox(this)">
                <div class="gallery-overlay">
                    <div class="text-white">
                        <h3 class="font-semibold">Industry Visit</h3>
                        <p class="text-sm opacity-90">Industrial exposure</p>
                    </div>
                </div>
            </div>

            <!-- Additional Images from scraped data -->
            <div class="gallery-item infrastructure" data-category="infrastructure">
                <img src="/assets/images/9.jpg" alt="Institute Facilities" class="w-full h-64 object-cover rounded-lg shadow-lg hover:shadow-xl transition-shadow cursor-pointer no-hover" onclick="openLightbox(this)">
                <div class="gallery-overlay">
                    <div class="text-white">
                        <h3 class="font-semibold">Institute Facilities</h3>
                        <p class="text-sm opacity-90">Modern infrastructure</p>
                    </div>
                </div>
            </div>

            <div class="gallery-item events" data-category="events">
                <img src="/assets/images/11.JPG" alt="Institute Event" class="w-full h-64 object-cover rounded-lg shadow-lg hover:shadow-xl transition-shadow cursor-pointer no-hover" onclick="openLightbox(this)">
                <div class="gallery-overlay">
                    <div class="text-white">
                        <h3 class="font-semibold">Institute Event</h3>
                        <p class="text-sm opacity-90">Special occasions</p>
                    </div>
                </div>
            </div>

            <div class="gallery-item activities" data-category="activities">
                <img src="/assets/images/13.JPG" alt="Student Activities" class="w-full h-64 object-cover rounded-lg shadow-lg hover:shadow-xl transition-shadow cursor-pointer no-hover" onclick="openLightbox(this)">
                <div class="gallery-overlay">
                    <div class="text-white">
                        <h3 class="font-semibold">Student Activities</h3>
                        <p class="text-sm opacity-90">Learning activities</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Lightbox Modal -->
<div id="lightbox" class="fixed inset-0 bg-black bg-opacity-90 z-50 hidden flex items-center justify-center p-4">
    <div class="relative max-w-4xl max-h-full">
        <button onclick="closeLightbox()" class="absolute top-4 right-4 text-white text-2xl hover:text-gray-300 transition-colors z-10">
            <i class="fas fa-times"></i>
        </button>
        <img id="lightbox-image" src="" alt="" class="max-w-full max-h-full object-contain">
        <div class="absolute bottom-4 left-4 text-white">
            <h3 id="lightbox-title" class="text-xl font-semibold mb-2"></h3>
            <p id="lightbox-description" class="text-gray-300"></p>
        </div>
        <button onclick="previousImage()" class="absolute left-4 top-1/2 transform -translate-y-1/2 text-white text-2xl hover:text-gray-300 transition-colors">
            <i class="fas fa-chevron-left"></i>
        </button>
        <button onclick="nextImage()" class="absolute right-4 top-1/2 transform -translate-y-1/2 text-white text-2xl hover:text-gray-300 transition-colors">
            <i class="fas fa-chevron-right"></i>
        </button>
    </div>
</div>

<!-- Statistics Section -->
<section class="py-16 bg-white">
    <div class="container mx-auto px-4">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-gray-800 mb-4">Our Achievements in Pictures</h2>
            <p class="text-gray-600 max-w-2xl mx-auto">Visual representation of our journey and milestones</p>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div class="text-center">
                <div class="bg-primary-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-camera text-2xl text-primary-600"></i>
                </div>
                <h3 class="text-3xl font-bold text-gray-800 mb-2">500+</h3>
                <p class="text-gray-600">Photos Captured</p>
            </div>
            <div class="text-center">
                <div class="bg-green-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-calendar text-2xl text-green-600"></i>
                </div>
                <h3 class="text-3xl font-bold text-gray-800 mb-2">50+</h3>
                <p class="text-gray-600">Events Documented</p>
            </div>
            <div class="text-center">
                <div class="bg-yellow-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-award text-2xl text-yellow-600"></i>
                </div>
                <h3 class="text-3xl font-bold text-gray-800 mb-2">25+</h3>
                <p class="text-gray-600">Achievements</p>
            </div>
            <div class="text-center">
                <div class="bg-red-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-users text-2xl text-red-600"></i>
                </div>
                <h3 class="text-3xl font-bold text-gray-800 mb-2">1000+</h3>
                <p class="text-gray-600">Students Featured</p>
            </div>
        </div>
    </div>
</section>
{% endblock %}

{% block extra_scripts %}
<script>
let currentImageIndex = 0;
let galleryImages = [];

// Gallery filtering
document.addEventListener('DOMContentLoaded', function() {
    const filterButtons = document.querySelectorAll('.gallery-filter');
    const galleryItems = document.querySelectorAll('.gallery-item');
    
    // Collect all images for lightbox navigation
    galleryImages = Array.from(document.querySelectorAll('.gallery-item img'));
    
    filterButtons.forEach(button => {
        button.addEventListener('click', function() {
            const filter = this.dataset.filter;
            
            // Update active button
            filterButtons.forEach(btn => {
                btn.classList.remove('active', 'bg-primary-600', 'text-white');
                btn.classList.add('bg-gray-200', 'text-gray-700');
            });
            this.classList.add('active', 'bg-primary-600', 'text-white');
            this.classList.remove('bg-gray-200', 'text-gray-700');
            
            // Filter gallery items
            galleryItems.forEach(item => {
                if (filter === 'all' || item.dataset.category === filter) {
                    item.style.display = 'block';
                } else {
                    item.style.display = 'none';
                }
            });
            
            // Update gallery images array for lightbox
            if (filter === 'all') {
                galleryImages = Array.from(document.querySelectorAll('.gallery-item img'));
            } else {
                galleryImages = Array.from(document.querySelectorAll(`.gallery-item[data-category="${filter}"] img`));
            }
        });
    });
});

function openLightbox(img) {
    const lightbox = document.getElementById('lightbox');
    const lightboxImage = document.getElementById('lightbox-image');
    const lightboxTitle = document.getElementById('lightbox-title');
    const lightboxDescription = document.getElementById('lightbox-description');
    
    currentImageIndex = galleryImages.indexOf(img);
    
    lightboxImage.src = img.src;
    lightboxImage.alt = img.alt;
    lightboxTitle.textContent = img.alt;
    lightboxDescription.textContent = img.closest('.gallery-item').querySelector('.gallery-overlay p')?.textContent || '';
    
    lightbox.classList.remove('hidden');
    document.body.classList.add('overflow-hidden');
}

function closeLightbox() {
    const lightbox = document.getElementById('lightbox');
    lightbox.classList.add('hidden');
    document.body.classList.remove('overflow-hidden');
}

function previousImage() {
    currentImageIndex = (currentImageIndex - 1 + galleryImages.length) % galleryImages.length;
    updateLightboxImage();
}

function nextImage() {
    currentImageIndex = (currentImageIndex + 1) % galleryImages.length;
    updateLightboxImage();
}

function updateLightboxImage() {
    const img = galleryImages[currentImageIndex];
    const lightboxImage = document.getElementById('lightbox-image');
    const lightboxTitle = document.getElementById('lightbox-title');
    const lightboxDescription = document.getElementById('lightbox-description');
    
    lightboxImage.src = img.src;
    lightboxImage.alt = img.alt;
    lightboxTitle.textContent = img.alt;
    lightboxDescription.textContent = img.closest('.gallery-item').querySelector('.gallery-overlay p')?.textContent || '';
}

// Keyboard navigation
document.addEventListener('keydown', function(e) {
    const lightbox = document.getElementById('lightbox');
    if (!lightbox.classList.contains('hidden')) {
        switch(e.key) {
            case 'Escape':
                closeLightbox();
                break;
            case 'ArrowLeft':
                previousImage();
                break;
            case 'ArrowRight':
                nextImage();
                break;
        }
    }
});
</script>
{% endblock %}
