{% extends "admin/base.html" %}

{% block page_title %}Media Library{% endblock %}
{% block page_description %}Manage images, documents, and media files{% endblock %}

{% block content %}
<!-- Header Actions -->
<div class="flex justify-between items-center mb-6">
    <div>
        <h3 class="text-lg font-semibold text-gray-900">Media Library</h3>
        <p class="text-sm text-gray-600">Upload and manage your media files</p>
    </div>
    <div class="flex space-x-2">
        <button onclick="openUploadModal()" class="bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors">
            <i class="fas fa-upload mr-2"></i>Upload Files
        </button>
        <button onclick="toggleView()" id="view-toggle" class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors">
            <i class="fas fa-list mr-2"></i>List View
        </button>
    </div>
</div>

<!-- Upload Progress -->
<div id="upload-progress" class="hidden mb-6">
    <div class="bg-white rounded-lg shadow p-4">
        <div class="flex items-center justify-between mb-2">
            <span class="text-sm font-medium text-gray-700">Uploading files...</span>
            <span id="upload-percentage" class="text-sm text-gray-500">0%</span>
        </div>
        <div class="w-full bg-gray-200 rounded-full h-2">
            <div id="upload-bar" class="bg-primary-600 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
        </div>
    </div>
</div>

<!-- Filters and Search -->
<div class="bg-white rounded-lg shadow mb-6 p-4">
    <div class="flex flex-col md:flex-row gap-4">
        <div class="flex-1">
            <input
                type="text"
                id="search-media"
                placeholder="Search media files..."
                class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
            >
        </div>
        <div class="flex gap-2">
            <select id="filter-type" class="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500">
                <option value="">All Types</option>
                <option value="image">Images</option>
                <option value="document">Documents</option>
                <option value="video">Videos</option>
                <option value="audio">Audio</option>
            </select>
            <select id="sort-by" class="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500">
                <option value="newest">Newest First</option>
                <option value="oldest">Oldest First</option>
                <option value="name">Name A-Z</option>
                <option value="size">File Size</option>
            </select>
        </div>
    </div>
</div>

<!-- Media Grid View -->
<div id="media-grid" class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
    <!-- Media items will be loaded here -->
    <div class="col-span-full flex justify-center py-12">
        <div class="text-center text-gray-500">
            <i class="fas fa-spinner fa-spin text-2xl mb-2"></i>
            <p>Loading media files...</p>
        </div>
    </div>
</div>

<!-- Media List View -->
<div id="media-list" class="hidden bg-white rounded-lg shadow overflow-hidden">
    <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        File
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Type
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Size
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Uploaded
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                    </th>
                </tr>
            </thead>
            <tbody id="media-list-body" class="bg-white divide-y divide-gray-200">
                <!-- Media list items will be loaded here -->
            </tbody>
        </table>
    </div>
</div>

<!-- Upload Modal -->
<div id="upload-modal" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
    <div class="bg-white rounded-lg shadow-xl w-full max-w-2xl">
        <div class="p-6 border-b border-gray-200">
            <div class="flex justify-between items-center">
                <h3 class="text-lg font-semibold text-gray-900">Upload Media Files</h3>
                <button onclick="closeUploadModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
        </div>

        <div class="p-6">
            <!-- Drag and Drop Area -->
            <div id="drop-zone" class="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-primary-500 transition-colors">
                <div class="space-y-4">
                    <div class="text-6xl text-gray-400">
                        <i class="fas fa-cloud-upload-alt"></i>
                    </div>
                    <div>
                        <p class="text-lg font-medium text-gray-900">Drop files here to upload</p>
                        <p class="text-sm text-gray-600">or click to browse files</p>
                    </div>
                    <div>
                        <input type="file" id="file-input" multiple accept="image/*,video/*,audio/*,.pdf,.doc,.docx,.txt" class="hidden">
                        <button onclick="document.getElementById('file-input').click()" class="bg-primary-600 text-white px-6 py-2 rounded-lg hover:bg-primary-700 transition-colors">
                            Choose Files
                        </button>
                    </div>
                    <p class="text-xs text-gray-500">
                        Supported formats: Images (JPG, PNG, GIF), Videos (MP4, AVI), Audio (MP3, WAV), Documents (PDF, DOC, TXT)
                    </p>
                </div>
            </div>

            <!-- Selected Files -->
            <div id="selected-files" class="mt-6 hidden">
                <h4 class="text-sm font-medium text-gray-900 mb-3">Selected Files</h4>
                <div id="file-list" class="space-y-2 max-h-40 overflow-y-auto">
                    <!-- Selected files will appear here -->
                </div>
            </div>

            <div class="flex justify-end space-x-4 mt-6">
                <button onclick="closeUploadModal()" class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors">
                    Cancel
                </button>
                <button id="upload-button" onclick="uploadFiles()" disabled class="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed">
                    Upload Files
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Media Details Modal -->
<div id="media-modal" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
    <div class="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <div class="p-6 border-b border-gray-200">
            <div class="flex justify-between items-center">
                <h3 class="text-lg font-semibold text-gray-900">Media Details</h3>
                <button onclick="closeMediaModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
        </div>

        <div class="p-6">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Media Preview -->
                <div>
                    <div id="media-preview" class="bg-gray-100 rounded-lg p-4 text-center">
                        <!-- Preview will be loaded here -->
                    </div>
                    <div class="mt-4 flex justify-center space-x-2">
                        <button onclick="copyMediaUrl()" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors text-sm">
                            <i class="fas fa-copy mr-2"></i>Copy URL
                        </button>
                        <button onclick="downloadMedia()" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors text-sm">
                            <i class="fas fa-download mr-2"></i>Download
                        </button>
                    </div>
                </div>

                <!-- Media Information -->
                <div>
                    <form id="media-form" class="space-y-4">
                        <input type="hidden" id="media-id" name="id">

                        <div>
                            <label for="media-alt-text" class="block text-sm font-medium text-gray-700 mb-2">Alt Text</label>
                            <input
                                type="text"
                                id="media-alt-text"
                                name="alt_text"
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                                placeholder="Describe this image for accessibility"
                            >
                        </div>

                        <div>
                            <label for="media-caption" class="block text-sm font-medium text-gray-700 mb-2">Caption</label>
                            <textarea
                                id="media-caption"
                                name="caption"
                                rows="3"
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                                placeholder="Optional caption for this media"
                            ></textarea>
                        </div>

                        <div class="grid grid-cols-2 gap-4 text-sm text-gray-600">
                            <div>
                                <label class="block font-medium text-gray-700 mb-1">File Name</label>
                                <p id="media-filename"></p>
                            </div>
                            <div>
                                <label class="block font-medium text-gray-700 mb-1">File Size</label>
                                <p id="media-filesize"></p>
                            </div>
                            <div>
                                <label class="block font-medium text-gray-700 mb-1">File Type</label>
                                <p id="media-filetype"></p>
                            </div>
                            <div>
                                <label class="block font-medium text-gray-700 mb-1">Uploaded</label>
                                <p id="media-uploaded"></p>
                            </div>
                        </div>

                        <div class="flex justify-between items-center pt-4 border-t border-gray-200">
                            <button type="button" onclick="deleteMediaFile()" class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors">
                                <i class="fas fa-trash mr-2"></i>Delete
                            </button>
                            <button type="submit" class="bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors">
                                Update Details
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
let currentView = 'grid';
let selectedFiles = [];
let currentMediaId = null;

document.addEventListener('DOMContentLoaded', function() {
    loadMediaFiles();
    setupEventListeners();
});

function setupEventListeners() {
    // Search and filters
    document.getElementById('search-media').addEventListener('input', debounce(loadMediaFiles, 300));
    document.getElementById('filter-type').addEventListener('change', loadMediaFiles);
    document.getElementById('sort-by').addEventListener('change', loadMediaFiles);

    // File input
    document.getElementById('file-input').addEventListener('change', handleFileSelect);

    // Drag and drop
    const dropZone = document.getElementById('drop-zone');

    dropZone.addEventListener('dragover', function(e) {
        e.preventDefault();
        this.classList.add('border-primary-500', 'bg-primary-50');
    });

    dropZone.addEventListener('dragleave', function(e) {
        e.preventDefault();
        this.classList.remove('border-primary-500', 'bg-primary-50');
    });

    dropZone.addEventListener('drop', function(e) {
        e.preventDefault();
        this.classList.remove('border-primary-500', 'bg-primary-50');

        const files = Array.from(e.dataTransfer.files);
        handleFileSelect({ target: { files: files } });
    });

    // Media form submission
    document.getElementById('media-form').addEventListener('submit', handleMediaUpdate);
}

async function loadMediaFiles() {
    showLoading();

    const search = document.getElementById('search-media').value;
    const type = document.getElementById('filter-type').value;
    const sort = document.getElementById('sort-by').value;

    try {
        const response = await fetch('/api/cms/media', {
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('access_token')}`
            }
        });

        if (response.ok) {
            const mediaFiles = await response.json();

            // Filter and sort media files
            let filteredFiles = mediaFiles;

            if (search) {
                filteredFiles = filteredFiles.filter(file =>
                    file.original_filename.toLowerCase().includes(search.toLowerCase()) ||
                    (file.alt_text && file.alt_text.toLowerCase().includes(search.toLowerCase()))
                );
            }

            if (type) {
                filteredFiles = filteredFiles.filter(file => {
                    const mimeType = file.mime_type || '';
                    switch (type) {
                        case 'image':
                            return mimeType.startsWith('image/');
                        case 'video':
                            return mimeType.startsWith('video/');
                        case 'audio':
                            return mimeType.startsWith('audio/');
                        case 'document':
                            return mimeType.includes('pdf') || mimeType.includes('document') || mimeType.includes('text');
                        default:
                            return true;
                    }
                });
            }

            // Sort files
            filteredFiles.sort((a, b) => {
                switch (sort) {
                    case 'newest':
                        return new Date(b.created_at) - new Date(a.created_at);
                    case 'oldest':
                        return new Date(a.created_at) - new Date(b.created_at);
                    case 'name':
                        return a.original_filename.localeCompare(b.original_filename);
                    case 'size':
                        return (b.file_size || 0) - (a.file_size || 0);
                    default:
                        return 0;
                }
            });

            if (currentView === 'grid') {
                displayMediaGrid(filteredFiles);
            } else {
                displayMediaList(filteredFiles);
            }
        } else {
            throw new Error('Failed to load media files');
        }
    } catch (error) {
        console.error('Error loading media files:', error);
        showToast('Error loading media files', 'error');
    } finally {
        hideLoading();
    }
}

function displayMediaGrid(mediaFiles) {
    const grid = document.getElementById('media-grid');

    if (mediaFiles.length === 0) {
        grid.innerHTML = `
            <div class="col-span-full flex flex-col items-center justify-center py-12">
                <i class="fas fa-images text-6xl text-gray-300 mb-4"></i>
                <p class="text-lg font-medium text-gray-500 mb-2">No media files found</p>
                <p class="text-sm text-gray-400">Upload your first media file to get started</p>
            </div>
        `;
        return;
    }

    grid.innerHTML = mediaFiles.map(file => {
        const isImage = file.mime_type && file.mime_type.startsWith('image/');
        const fileUrl = `/uploads/${file.filename}`;

        return `
            <div class="bg-white rounded-lg shadow hover:shadow-lg transition-shadow cursor-pointer" onclick="viewMediaDetails(${file.id})">
                <div class="aspect-square bg-gray-100 rounded-t-lg overflow-hidden">
                    ${isImage ?
                        `<img src="${fileUrl}" alt="${file.alt_text || file.original_filename}" class="w-full h-full object-cover">` :
                        `<div class="w-full h-full flex items-center justify-center">
                            <i class="fas ${getFileIcon(file.mime_type)} text-4xl text-gray-400"></i>
                        </div>`
                    }
                </div>
                <div class="p-3">
                    <p class="text-sm font-medium text-gray-900 truncate" title="${file.original_filename}">
                        ${file.original_filename}
                    </p>
                    <p class="text-xs text-gray-500 mt-1">
                        ${formatFileSize(file.file_size)}
                    </p>
                </div>
            </div>
        `;
    }).join('');
}

function displayMediaList(mediaFiles) {
    const tbody = document.getElementById('media-list-body');

    if (mediaFiles.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="5" class="px-6 py-12 text-center text-gray-500">
                    <i class="fas fa-images text-4xl mb-4 text-gray-300"></i>
                    <p class="text-lg font-medium mb-2">No media files found</p>
                    <p class="text-sm">Upload your first media file to get started</p>
                </td>
            </tr>
        `;
        return;
    }

    tbody.innerHTML = mediaFiles.map(file => {
        const isImage = file.mime_type && file.mime_type.startsWith('image/');
        const fileUrl = `/uploads/${file.filename}`;

        return `
            <tr class="hover:bg-gray-50 cursor-pointer" onclick="viewMediaDetails(${file.id})">
                <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-gray-100 rounded overflow-hidden mr-3">
                            ${isImage ?
                                `<img src="${fileUrl}" alt="${file.alt_text || file.original_filename}" class="w-full h-full object-cover">` :
                                `<div class="w-full h-full flex items-center justify-center">
                                    <i class="fas ${getFileIcon(file.mime_type)} text-gray-400"></i>
                                </div>`
                            }
                        </div>
                        <div>
                            <div class="text-sm font-medium text-gray-900">${file.original_filename}</div>
                            ${file.alt_text ? `<div class="text-xs text-gray-500">${file.alt_text}</div>` : ''}
                        </div>
                    </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getTypeBadgeClass(file.mime_type)}">
                        ${getFileType(file.mime_type)}
                    </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    ${formatFileSize(file.file_size)}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    ${new Date(file.created_at).toLocaleDateString()}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div class="flex space-x-2">
                        <button onclick="event.stopPropagation(); copyMediaUrl('${fileUrl}')" class="text-blue-600 hover:text-blue-800" title="Copy URL">
                            <i class="fas fa-copy"></i>
                        </button>
                        <a href="${fileUrl}" download="${file.original_filename}" onclick="event.stopPropagation()" class="text-green-600 hover:text-green-800" title="Download">
                            <i class="fas fa-download"></i>
                        </a>
                        <button onclick="event.stopPropagation(); deleteMediaFile(${file.id})" class="text-red-600 hover:text-red-800" title="Delete">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }).join('');
}

function getFileIcon(mimeType) {
    if (!mimeType) return 'fa-file';

    if (mimeType.startsWith('image/')) return 'fa-image';
    if (mimeType.startsWith('video/')) return 'fa-video';
    if (mimeType.startsWith('audio/')) return 'fa-music';
    if (mimeType.includes('pdf')) return 'fa-file-pdf';
    if (mimeType.includes('document') || mimeType.includes('word')) return 'fa-file-word';
    if (mimeType.includes('text')) return 'fa-file-alt';

    return 'fa-file';
}

function getFileType(mimeType) {
    if (!mimeType) return 'Unknown';

    if (mimeType.startsWith('image/')) return 'Image';
    if (mimeType.startsWith('video/')) return 'Video';
    if (mimeType.startsWith('audio/')) return 'Audio';
    if (mimeType.includes('pdf')) return 'PDF';
    if (mimeType.includes('document') || mimeType.includes('word')) return 'Document';
    if (mimeType.includes('text')) return 'Text';

    return 'File';
}

function getTypeBadgeClass(mimeType) {
    if (!mimeType) return 'bg-gray-100 text-gray-800';

    if (mimeType.startsWith('image/')) return 'bg-green-100 text-green-800';
    if (mimeType.startsWith('video/')) return 'bg-blue-100 text-blue-800';
    if (mimeType.startsWith('audio/')) return 'bg-purple-100 text-purple-800';
    if (mimeType.includes('pdf')) return 'bg-red-100 text-red-800';
    if (mimeType.includes('document')) return 'bg-yellow-100 text-yellow-800';

    return 'bg-gray-100 text-gray-800';
}

function formatFileSize(bytes) {
    if (!bytes) return '0 B';

    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
}

function toggleView() {
    const gridView = document.getElementById('media-grid');
    const listView = document.getElementById('media-list');
    const toggleButton = document.getElementById('view-toggle');

    if (currentView === 'grid') {
        currentView = 'list';
        gridView.classList.add('hidden');
        listView.classList.remove('hidden');
        toggleButton.innerHTML = '<i class="fas fa-th mr-2"></i>Grid View';
        loadMediaFiles(); // Reload for list view
    } else {
        currentView = 'grid';
        listView.classList.add('hidden');
        gridView.classList.remove('hidden');
        toggleButton.innerHTML = '<i class="fas fa-list mr-2"></i>List View';
        loadMediaFiles(); // Reload for grid view
    }
}

function openUploadModal() {
    selectedFiles = [];
    document.getElementById('file-input').value = '';
    document.getElementById('selected-files').classList.add('hidden');
    document.getElementById('upload-button').disabled = true;
    document.getElementById('upload-modal').classList.remove('hidden');
}

function closeUploadModal() {
    document.getElementById('upload-modal').classList.add('hidden');
}

function handleFileSelect(event) {
    const files = Array.from(event.target.files);
    selectedFiles = files;

    if (files.length > 0) {
        displaySelectedFiles(files);
        document.getElementById('selected-files').classList.remove('hidden');
        document.getElementById('upload-button').disabled = false;
    } else {
        document.getElementById('selected-files').classList.add('hidden');
        document.getElementById('upload-button').disabled = true;
    }
}

function displaySelectedFiles(files) {
    const fileList = document.getElementById('file-list');

    fileList.innerHTML = files.map((file, index) => `
        <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
            <div class="flex items-center">
                <i class="fas ${getFileIcon(file.type)} mr-2 text-gray-500"></i>
                <span class="text-sm text-gray-900">${file.name}</span>
                <span class="text-xs text-gray-500 ml-2">(${formatFileSize(file.size)})</span>
            </div>
            <button onclick="removeFile(${index})" class="text-red-500 hover:text-red-700">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `).join('');
}

function removeFile(index) {
    selectedFiles.splice(index, 1);

    if (selectedFiles.length > 0) {
        displaySelectedFiles(selectedFiles);
    } else {
        document.getElementById('selected-files').classList.add('hidden');
        document.getElementById('upload-button').disabled = true;
    }
}

async function uploadFiles() {
    if (selectedFiles.length === 0) return;

    const progressContainer = document.getElementById('upload-progress');
    const progressBar = document.getElementById('upload-bar');
    const progressText = document.getElementById('upload-percentage');

    progressContainer.classList.remove('hidden');
    closeUploadModal();

    let uploadedCount = 0;
    const totalFiles = selectedFiles.length;

    try {
        for (const file of selectedFiles) {
            const formData = new FormData();
            formData.append('file', file);

            const response = await fetch('/api/cms/media/upload', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('access_token')}`
                },
                body: formData
            });

            if (!response.ok) {
                throw new Error(`Failed to upload ${file.name}`);
            }

            uploadedCount++;
            const percentage = Math.round((uploadedCount / totalFiles) * 100);
            progressBar.style.width = percentage + '%';
            progressText.textContent = percentage + '%';
        }

        showToast(`Successfully uploaded ${uploadedCount} file(s)`, 'success');
        loadMediaFiles();

    } catch (error) {
        console.error('Upload error:', error);
        showToast(error.message, 'error');
    } finally {
        setTimeout(() => {
            progressContainer.classList.add('hidden');
            progressBar.style.width = '0%';
            progressText.textContent = '0%';
        }, 2000);
    }
}

async function viewMediaDetails(mediaId) {
    try {
        const response = await fetch('/api/cms/media', {
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('access_token')}`
            }
        });

        if (response.ok) {
            const mediaFiles = await response.json();
            const media = mediaFiles.find(f => f.id === mediaId);

            if (media) {
                currentMediaId = mediaId;

                // Populate modal
                document.getElementById('media-id').value = media.id;
                document.getElementById('media-alt-text').value = media.alt_text || '';
                document.getElementById('media-caption').value = media.caption || '';
                document.getElementById('media-filename').textContent = media.original_filename;
                document.getElementById('media-filesize').textContent = formatFileSize(media.file_size);
                document.getElementById('media-filetype').textContent = getFileType(media.mime_type);
                document.getElementById('media-uploaded').textContent = new Date(media.created_at).toLocaleDateString();

                // Show preview
                const preview = document.getElementById('media-preview');
                const fileUrl = `/uploads/${media.filename}`;
                const isImage = media.mime_type && media.mime_type.startsWith('image/');

                if (isImage) {
                    preview.innerHTML = `<img src="${fileUrl}" alt="${media.alt_text || media.original_filename}" class="max-w-full max-h-64 mx-auto rounded">`;
                } else {
                    preview.innerHTML = `
                        <div class="text-center py-8">
                            <i class="fas ${getFileIcon(media.mime_type)} text-6xl text-gray-400 mb-4"></i>
                            <p class="text-lg font-medium text-gray-900">${media.original_filename}</p>
                            <p class="text-sm text-gray-500">${getFileType(media.mime_type)} • ${formatFileSize(media.file_size)}</p>
                        </div>
                    `;
                }

                document.getElementById('media-modal').classList.remove('hidden');
            }
        }
    } catch (error) {
        console.error('Error loading media details:', error);
        showToast('Error loading media details', 'error');
    }
}

function closeMediaModal() {
    document.getElementById('media-modal').classList.add('hidden');
    currentMediaId = null;
}

async function handleMediaUpdate(e) {
    e.preventDefault();

    if (!currentMediaId) return;

    const formData = new FormData(e.target);
    const updateData = {
        alt_text: formData.get('alt_text'),
        caption: formData.get('caption')
    };

    try {
        showLoading();

        const response = await fetch(`/api/cms/media/${currentMediaId}`, {
            method: 'PUT',
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(updateData)
        });

        if (response.ok) {
            showToast('Media details updated successfully', 'success');
            closeMediaModal();
            loadMediaFiles();
        } else {
            throw new Error('Failed to update media details');
        }
    } catch (error) {
        console.error('Error updating media:', error);
        showToast(error.message, 'error');
    } finally {
        hideLoading();
    }
}

function copyMediaUrl(url) {
    if (!url && currentMediaId) {
        // Get URL from current media
        const response = fetch('/api/cms/media', {
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('access_token')}`
            }
        }).then(res => res.json()).then(files => {
            const media = files.find(f => f.id === currentMediaId);
            if (media) {
                url = `/uploads/${media.filename}`;
                copyToClipboard(window.location.origin + url);
            }
        });
        return;
    }

    copyToClipboard(window.location.origin + url);
}

function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(() => {
        showToast('URL copied to clipboard', 'success');
    }).catch(() => {
        showToast('Failed to copy URL', 'error');
    });
}

function downloadMedia() {
    if (!currentMediaId) return;

    fetch('/api/cms/media', {
        headers: {
            'Authorization': `Bearer ${localStorage.getItem('access_token')}`
        }
    }).then(res => res.json()).then(files => {
        const media = files.find(f => f.id === currentMediaId);
        if (media) {
            const link = document.createElement('a');
            link.href = `/uploads/${media.filename}`;
            link.download = media.original_filename;
            link.click();
        }
    });
}

async function deleteMediaFile(mediaId) {
    if (!mediaId && currentMediaId) {
        mediaId = currentMediaId;
    }

    if (!confirm('Are you sure you want to delete this media file?')) {
        return;
    }

    try {
        showLoading();

        const response = await fetch(`/api/cms/media/${mediaId}`, {
            method: 'DELETE',
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('access_token')}`
            }
        });

        if (response.ok) {
            showToast('Media file deleted successfully', 'success');
            if (currentMediaId === mediaId) {
                closeMediaModal();
            }
            loadMediaFiles();
        } else {
            throw new Error('Failed to delete media file');
        }
    } catch (error) {
        console.error('Error deleting media file:', error);
        showToast(error.message, 'error');
    } finally {
        hideLoading();
    }
}

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}
</script>
{% endblock %}