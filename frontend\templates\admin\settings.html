{% extends "admin/base.html" %}

{% block page_title %}Site Settings{% endblock %}
{% block page_description %}Configure website settings and preferences{% endblock %}

{% block content %}
<!-- Settings Tabs -->
<div class="mb-6">
    <nav class="flex space-x-8" aria-label="Tabs">
        <button onclick="showTab('general')" class="settings-tab active whitespace-nowrap py-2 px-1 border-b-2 border-primary-500 font-medium text-sm text-primary-600">
            General Settings
        </button>
        <button onclick="showTab('contact')" class="settings-tab whitespace-nowrap py-2 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700 hover:border-gray-300">
            Contact Information
        </button>
        <button onclick="showTab('social')" class="settings-tab whitespace-nowrap py-2 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700 hover:border-gray-300">
            Social Media
        </button>
        <button onclick="showTab('seo')" class="settings-tab whitespace-nowrap py-2 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700 hover:border-gray-300">
            SEO Settings
        </button>
        <button onclick="showTab('advanced')" class="settings-tab whitespace-nowrap py-2 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700 hover:border-gray-300">
            Advanced
        </button>
    </nav>
</div>

<!-- General Settings Tab -->
<div id="general-tab" class="settings-content">
    <div class="bg-white rounded-lg shadow">
        <div class="p-6 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">General Settings</h3>
            <p class="text-sm text-gray-600">Basic website configuration</p>
        </div>
        <form id="general-form" class="p-6 space-y-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="site-title" class="block text-sm font-medium text-gray-700 mb-2">Site Title</label>
                    <input 
                        type="text" 
                        id="site-title" 
                        name="site_title"
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                        placeholder="S.N. Pvt. Industrial Training Institute"
                    >
                </div>
                
                <div>
                    <label for="site-tagline" class="block text-sm font-medium text-gray-700 mb-2">Site Tagline</label>
                    <input 
                        type="text" 
                        id="site-tagline" 
                        name="site_tagline"
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                        placeholder="Excellence in Technical Education"
                    >
                </div>
            </div>
            
            <div>
                <label for="site-description" class="block text-sm font-medium text-gray-700 mb-2">Site Description</label>
                <textarea 
                    id="site-description" 
                    name="site_description" 
                    rows="3"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                    placeholder="Brief description of your institute..."
                ></textarea>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="admin-email" class="block text-sm font-medium text-gray-700 mb-2">Admin Email</label>
                    <input 
                        type="email" 
                        id="admin-email" 
                        name="admin_email"
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                        placeholder="<EMAIL>"
                    >
                </div>
                
                <div>
                    <label for="timezone" class="block text-sm font-medium text-gray-700 mb-2">Timezone</label>
                    <select 
                        id="timezone" 
                        name="timezone"
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                    >
                        <option value="Asia/Kolkata">Asia/Kolkata (IST)</option>
                        <option value="UTC">UTC</option>
                    </select>
                </div>
            </div>
            
            <div class="flex justify-end">
                <button type="submit" class="bg-primary-600 text-white px-6 py-2 rounded-lg hover:bg-primary-700 transition-colors">
                    Save General Settings
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Contact Information Tab -->
<div id="contact-tab" class="settings-content hidden">
    <div class="bg-white rounded-lg shadow">
        <div class="p-6 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">Contact Information</h3>
            <p class="text-sm text-gray-600">Institute contact details</p>
        </div>
        <form id="contact-form" class="p-6 space-y-6">
            <div>
                <label for="institute-address" class="block text-sm font-medium text-gray-700 mb-2">Address</label>
                <textarea 
                    id="institute-address" 
                    name="institute_address" 
                    rows="3"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                    placeholder="D-117, Kaka Colony, Gandhi Vidhya Mandir..."
                ></textarea>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="institute-phone" class="block text-sm font-medium text-gray-700 mb-2">Phone Number</label>
                    <input 
                        type="tel" 
                        id="institute-phone" 
                        name="institute_phone"
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                        placeholder="01564-275628"
                    >
                </div>
                
                <div>
                    <label for="institute-mobile" class="block text-sm font-medium text-gray-700 mb-2">Mobile Number</label>
                    <input 
                        type="tel" 
                        id="institute-mobile" 
                        name="institute_mobile"
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                        placeholder="9414947801"
                    >
                </div>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="institute-email" class="block text-sm font-medium text-gray-700 mb-2">Email Address</label>
                    <input 
                        type="email" 
                        id="institute-email" 
                        name="institute_email"
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                        placeholder="<EMAIL>"
                    >
                </div>
                
                <div>
                    <label for="institute-website" class="block text-sm font-medium text-gray-700 mb-2">Website URL</label>
                    <input 
                        type="url" 
                        id="institute-website" 
                        name="institute_website"
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                        placeholder="https://snpitc.in"
                    >
                </div>
            </div>
            
            <div>
                <label for="office-hours" class="block text-sm font-medium text-gray-700 mb-2">Office Hours</label>
                <textarea 
                    id="office-hours" 
                    name="office_hours" 
                    rows="3"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                    placeholder="Monday - Friday: 9:00 AM - 5:00 PM..."
                ></textarea>
            </div>
            
            <div class="flex justify-end">
                <button type="submit" class="bg-primary-600 text-white px-6 py-2 rounded-lg hover:bg-primary-700 transition-colors">
                    Save Contact Information
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Social Media Tab -->
<div id="social-tab" class="settings-content hidden">
    <div class="bg-white rounded-lg shadow">
        <div class="p-6 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">Social Media Links</h3>
            <p class="text-sm text-gray-600">Social media profiles and links</p>
        </div>
        <form id="social-form" class="p-6 space-y-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="facebook-url" class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fab fa-facebook-f mr-2"></i>Facebook URL
                    </label>
                    <input 
                        type="url" 
                        id="facebook-url" 
                        name="facebook_url"
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                        placeholder="https://facebook.com/snpitc"
                    >
                </div>
                
                <div>
                    <label for="twitter-url" class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fab fa-twitter mr-2"></i>Twitter URL
                    </label>
                    <input 
                        type="url" 
                        id="twitter-url" 
                        name="twitter_url"
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                        placeholder="https://twitter.com/snpitc"
                    >
                </div>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="linkedin-url" class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fab fa-linkedin-in mr-2"></i>LinkedIn URL
                    </label>
                    <input 
                        type="url" 
                        id="linkedin-url" 
                        name="linkedin_url"
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                        placeholder="https://linkedin.com/company/snpitc"
                    >
                </div>
                
                <div>
                    <label for="youtube-url" class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fab fa-youtube mr-2"></i>YouTube URL
                    </label>
                    <input 
                        type="url" 
                        id="youtube-url" 
                        name="youtube_url"
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                        placeholder="https://youtube.com/c/snpitc"
                    >
                </div>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="instagram-url" class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fab fa-instagram mr-2"></i>Instagram URL
                    </label>
                    <input 
                        type="url" 
                        id="instagram-url" 
                        name="instagram_url"
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                        placeholder="https://instagram.com/snpitc"
                    >
                </div>
                
                <div>
                    <label for="whatsapp-number" class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fab fa-whatsapp mr-2"></i>WhatsApp Number
                    </label>
                    <input 
                        type="tel" 
                        id="whatsapp-number" 
                        name="whatsapp_number"
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                        placeholder="+919414947801"
                    >
                </div>
            </div>
            
            <div class="flex justify-end">
                <button type="submit" class="bg-primary-600 text-white px-6 py-2 rounded-lg hover:bg-primary-700 transition-colors">
                    Save Social Media Settings
                </button>
            </div>
        </form>
    </div>
</div>

<!-- SEO Settings Tab -->
<div id="seo-tab" class="settings-content hidden">
    <div class="bg-white rounded-lg shadow">
        <div class="p-6 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">SEO Settings</h3>
            <p class="text-sm text-gray-600">Search engine optimization configuration</p>
        </div>
        <form id="seo-form" class="p-6 space-y-6">
            <div>
                <label for="meta-description" class="block text-sm font-medium text-gray-700 mb-2">Default Meta Description</label>
                <textarea 
                    id="meta-description" 
                    name="meta_description" 
                    rows="3"
                    maxlength="160"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                    placeholder="Default meta description for your website..."
                ></textarea>
                <p class="text-xs text-gray-500 mt-1"><span id="meta-desc-count">0</span>/160 characters</p>
            </div>
            
            <div>
                <label for="meta-keywords" class="block text-sm font-medium text-gray-700 mb-2">Default Meta Keywords</label>
                <input 
                    type="text" 
                    id="meta-keywords" 
                    name="meta_keywords"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                    placeholder="industrial training, technical education, ITI, NCVT"
                >
                <p class="text-xs text-gray-500 mt-1">Comma-separated keywords</p>
            </div>
            
            <div>
                <label for="google-analytics" class="block text-sm font-medium text-gray-700 mb-2">Google Analytics ID</label>
                <input 
                    type="text" 
                    id="google-analytics" 
                    name="google_analytics_id"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                    placeholder="G-XXXXXXXXXX"
                >
            </div>
            
            <div>
                <label for="google-search-console" class="block text-sm font-medium text-gray-700 mb-2">Google Search Console Verification</label>
                <input 
                    type="text" 
                    id="google-search-console" 
                    name="google_search_console"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                    placeholder="Verification meta tag content"
                >
            </div>
            
            <div class="flex items-center">
                <input 
                    type="checkbox" 
                    id="robots-index" 
                    name="robots_index"
                    checked
                    class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                >
                <label for="robots-index" class="ml-2 text-sm text-gray-700">
                    Allow search engines to index this site
                </label>
            </div>
            
            <div class="flex justify-end">
                <button type="submit" class="bg-primary-600 text-white px-6 py-2 rounded-lg hover:bg-primary-700 transition-colors">
                    Save SEO Settings
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Advanced Settings Tab -->
<div id="advanced-tab" class="settings-content hidden">
    <div class="bg-white rounded-lg shadow">
        <div class="p-6 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">Advanced Settings</h3>
            <p class="text-sm text-gray-600">Advanced configuration options</p>
        </div>
        <form id="advanced-form" class="p-6 space-y-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="maintenance-mode" class="flex items-center">
                        <input 
                            type="checkbox" 
                            id="maintenance-mode" 
                            name="maintenance_mode"
                            class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                        >
                        <span class="ml-2 text-sm text-gray-700">Maintenance Mode</span>
                    </label>
                    <p class="text-xs text-gray-500 mt-1">Enable to show maintenance page to visitors</p>
                </div>
                
                <div>
                    <label for="user-registration" class="flex items-center">
                        <input 
                            type="checkbox" 
                            id="user-registration" 
                            name="user_registration"
                            class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                        >
                        <span class="ml-2 text-sm text-gray-700">Allow User Registration</span>
                    </label>
                    <p class="text-xs text-gray-500 mt-1">Allow visitors to register accounts</p>
                </div>
            </div>
            
            <div>
                <label for="custom-css" class="block text-sm font-medium text-gray-700 mb-2">Custom CSS</label>
                <textarea 
                    id="custom-css" 
                    name="custom_css" 
                    rows="6"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 font-mono text-sm"
                    placeholder="/* Add your custom CSS here */"
                ></textarea>
            </div>
            
            <div>
                <label for="custom-js" class="block text-sm font-medium text-gray-700 mb-2">Custom JavaScript</label>
                <textarea 
                    id="custom-js" 
                    name="custom_js" 
                    rows="6"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 font-mono text-sm"
                    placeholder="// Add your custom JavaScript here"
                ></textarea>
            </div>
            
            <div class="flex justify-end">
                <button type="submit" class="bg-primary-600 text-white px-6 py-2 rounded-lg hover:bg-primary-700 transition-colors">
                    Save Advanced Settings
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    loadSettings();
    setupEventListeners();
});

function setupEventListeners() {
    // Meta description character counter
    document.getElementById('meta-description').addEventListener('input', function() {
        const count = this.value.length;
        document.getElementById('meta-desc-count').textContent = count;
        
        if (count > 160) {
            this.classList.add('border-red-500');
        } else {
            this.classList.remove('border-red-500');
        }
    });
    
    // Form submissions
    document.getElementById('general-form').addEventListener('submit', handleFormSubmit);
    document.getElementById('contact-form').addEventListener('submit', handleFormSubmit);
    document.getElementById('social-form').addEventListener('submit', handleFormSubmit);
    document.getElementById('seo-form').addEventListener('submit', handleFormSubmit);
    document.getElementById('advanced-form').addEventListener('submit', handleFormSubmit);
}

function showTab(tabName) {
    // Hide all tabs
    document.querySelectorAll('.settings-content').forEach(tab => {
        tab.classList.add('hidden');
    });
    
    // Remove active class from all tab buttons
    document.querySelectorAll('.settings-tab').forEach(button => {
        button.classList.remove('active', 'border-primary-500', 'text-primary-600');
        button.classList.add('border-transparent', 'text-gray-500');
    });
    
    // Show selected tab
    document.getElementById(tabName + '-tab').classList.remove('hidden');
    
    // Add active class to selected tab button
    event.target.classList.add('active', 'border-primary-500', 'text-primary-600');
    event.target.classList.remove('border-transparent', 'text-gray-500');
}

async function loadSettings() {
    try {
        const response = await fetch('/api/cms/settings', {
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('access_token')}`
            }
        });
        
        if (response.ok) {
            const settings = await response.json();
            
            // Populate form fields with existing settings
            settings.forEach(setting => {
                const field = document.querySelector(`[name="${setting.key}"]`);
                if (field) {
                    if (field.type === 'checkbox') {
                        field.checked = setting.value === 'true';
                    } else {
                        field.value = setting.value || '';
                    }
                }
            });
            
            // Update character counter for meta description
            const metaDesc = document.getElementById('meta-description');
            if (metaDesc.value) {
                document.getElementById('meta-desc-count').textContent = metaDesc.value.length;
            }
        }
    } catch (error) {
        console.error('Error loading settings:', error);
        showToast('Error loading settings', 'error');
    }
}

async function handleFormSubmit(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const settings = [];
    
    // Convert form data to settings array
    for (let [key, value] of formData.entries()) {
        settings.push({
            key: key,
            value: value,
            data_type: typeof value === 'boolean' ? 'boolean' : 'string',
            is_public: true
        });
    }
    
    // Handle checkboxes that aren't checked (they won't be in formData)
    const checkboxes = e.target.querySelectorAll('input[type="checkbox"]');
    checkboxes.forEach(checkbox => {
        if (!formData.has(checkbox.name)) {
            settings.push({
                key: checkbox.name,
                value: 'false',
                data_type: 'boolean',
                is_public: true
            });
        }
    });
    
    try {
        showLoading();
        
        // Save each setting
        for (const setting of settings) {
            const response = await fetch('/api/cms/settings', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(setting)
            });
            
            if (!response.ok) {
                throw new Error('Failed to save setting: ' + setting.key);
            }
        }
        
        showToast('Settings saved successfully', 'success');
        
    } catch (error) {
        console.error('Error saving settings:', error);
        showToast(error.message, 'error');
    } finally {
        hideLoading();
    }
}
</script>
{% endblock %}
