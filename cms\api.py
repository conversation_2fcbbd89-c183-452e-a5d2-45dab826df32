"""
API routes for CMS
"""
from datetime import timed<PERSON><PERSON>
from typing import List
import os
import uuid
from pathlib import Path
from fastapi import APIRouter, Depends, HTTPException, status, Request, UploadFile, File
from fastapi.security import HTTPBearer
from sqlalchemy.orm import Session
from . import crud, models, schemas, auth
from .database import get_db

# Create API router
router = APIRouter(prefix="/api/cms", tags=["CMS"])

# Authentication routes
@router.post("/auth/login", response_model=schemas.Token)
async def login(login_data: schemas.LoginRequest, db: Session = Depends(get_db)):
    """Authenticate user and return access token"""
    user = auth.authenticate_user(db, login_data.username, login_data.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    access_token_expires = timedelta(minutes=auth.ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = auth.create_access_token(
        data={"sub": user.username}, expires_delta=access_token_expires
    )
    return {"access_token": access_token, "token_type": "bearer"}

@router.get("/auth/me", response_model=schemas.User)
async def read_users_me(current_user: models.User = Depends(auth.get_current_active_user)):
    """Get current user information"""
    return current_user

# User management routes
@router.get("/users", response_model=List[schemas.User])
async def read_users(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(auth.require_admin)
):
    """Get all users (admin only)"""
    users = crud.get_users(db, skip=skip, limit=limit)
    return users

@router.post("/users", response_model=schemas.User)
async def create_user(
    user: schemas.UserCreate,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(auth.require_admin)
):
    """Create new user (admin only)"""
    db_user = crud.get_user_by_username(db, username=user.username)
    if db_user:
        raise HTTPException(status_code=400, detail="Username already registered")
    
    db_user = crud.get_user_by_email(db, email=user.email)
    if db_user:
        raise HTTPException(status_code=400, detail="Email already registered")
    
    return crud.create_user(db=db, user=user)

@router.get("/users/{user_id}", response_model=schemas.User)
async def read_user(
    user_id: int,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(auth.require_admin)
):
    """Get user by ID (admin only)"""
    db_user = crud.get_user(db, user_id=user_id)
    if db_user is None:
        raise HTTPException(status_code=404, detail="User not found")
    return db_user

@router.put("/users/{user_id}", response_model=schemas.User)
async def update_user(
    user_id: int,
    user_update: schemas.UserUpdate,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(auth.require_admin)
):
    """Update user (admin only)"""
    db_user = crud.update_user(db, user_id=user_id, user_update=user_update)
    if db_user is None:
        raise HTTPException(status_code=404, detail="User not found")
    return db_user

@router.delete("/users/{user_id}", response_model=schemas.MessageResponse)
async def delete_user(
    user_id: int,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(auth.require_admin)
):
    """Delete user (admin only)"""
    if user_id == current_user.id:
        raise HTTPException(status_code=400, detail="Cannot delete yourself")
    
    success = crud.delete_user(db, user_id=user_id)
    if not success:
        raise HTTPException(status_code=404, detail="User not found")
    return {"message": "User deleted successfully"}

# Page management routes
@router.get("/pages", response_model=List[schemas.Page])
async def read_pages(
    skip: int = 0,
    limit: int = 100,
    published_only: bool = False,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(auth.get_current_active_user)
):
    """Get all pages"""
    pages = crud.get_pages(db, skip=skip, limit=limit, published_only=published_only)
    return pages

@router.post("/pages", response_model=schemas.Page)
async def create_page(
    page: schemas.PageCreate,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(auth.require_editor)
):
    """Create new page"""
    db_page = crud.get_page_by_slug(db, slug=page.slug)
    if db_page:
        raise HTTPException(status_code=400, detail="Page with this slug already exists")
    
    return crud.create_page(db=db, page=page, author_id=current_user.id)

@router.get("/pages/{page_id}", response_model=schemas.Page)
async def read_page(
    page_id: int,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(auth.get_current_active_user)
):
    """Get page by ID"""
    db_page = crud.get_page(db, page_id=page_id)
    if db_page is None:
        raise HTTPException(status_code=404, detail="Page not found")
    return db_page

@router.put("/pages/{page_id}", response_model=schemas.Page)
async def update_page(
    page_id: int,
    page_update: schemas.PageUpdate,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(auth.require_editor)
):
    """Update page"""
    db_page = crud.update_page(db, page_id=page_id, page_update=page_update)
    if db_page is None:
        raise HTTPException(status_code=404, detail="Page not found")
    return db_page

@router.delete("/pages/{page_id}", response_model=schemas.MessageResponse)
async def delete_page(
    page_id: int,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(auth.require_editor)
):
    """Delete page"""
    success = crud.delete_page(db, page_id=page_id)
    if not success:
        raise HTTPException(status_code=404, detail="Page not found")
    return {"message": "Page deleted successfully"}

# Menu management routes
@router.get("/menu", response_model=List[schemas.MenuItem])
async def read_menu_items(
    active_only: bool = False,
    db: Session = Depends(get_db)
):
    """Get menu items"""
    menu_items = crud.get_menu_items(db, active_only=active_only)
    return menu_items

@router.post("/menu", response_model=schemas.MenuItem)
async def create_menu_item(
    menu_item: schemas.MenuItemCreate,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(auth.require_editor)
):
    """Create new menu item"""
    return crud.create_menu_item(db=db, menu_item=menu_item)

@router.put("/menu/{item_id}", response_model=schemas.MenuItem)
async def update_menu_item(
    item_id: int,
    menu_update: schemas.MenuItemUpdate,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(auth.require_editor)
):
    """Update menu item"""
    db_menu_item = crud.update_menu_item(db, item_id=item_id, menu_update=menu_update)
    if db_menu_item is None:
        raise HTTPException(status_code=404, detail="Menu item not found")
    return db_menu_item

@router.delete("/menu/{item_id}", response_model=schemas.MessageResponse)
async def delete_menu_item(
    item_id: int,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(auth.require_editor)
):
    """Delete menu item"""
    success = crud.delete_menu_item(db, item_id=item_id)
    if not success:
        raise HTTPException(status_code=404, detail="Menu item not found")
    return {"message": "Menu item deleted successfully"}

# Site settings routes
@router.get("/settings", response_model=List[schemas.SiteSetting])
async def read_site_settings(
    public_only: bool = False,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(auth.get_current_active_user)
):
    """Get site settings"""
    settings = crud.get_site_settings(db, public_only=public_only)
    return settings

@router.post("/settings", response_model=schemas.SiteSetting)
async def create_or_update_setting(
    setting: schemas.SiteSettingCreate,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(auth.require_admin)
):
    """Create or update site setting"""
    return crud.create_or_update_setting(db=db, setting=setting)

@router.delete("/settings/{key}", response_model=schemas.MessageResponse)
async def delete_setting(
    key: str,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(auth.require_admin)
):
    """Delete site setting"""
    success = crud.delete_site_setting(db, key=key)
    if not success:
        raise HTTPException(status_code=404, detail="Setting not found")
    return {"message": "Setting deleted successfully"}

# Faculty management routes
@router.get("/faculty", response_model=List[schemas.Faculty])
async def read_faculty(
    active_only: bool = False,
    db: Session = Depends(get_db)
):
    """Get faculty list"""
    faculty = crud.get_faculty_list(db, active_only=active_only)
    return faculty

@router.post("/faculty", response_model=schemas.Faculty)
async def create_faculty(
    faculty: schemas.FacultyCreate,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(auth.require_editor)
):
    """Create new faculty member"""
    return crud.create_faculty(db=db, faculty=faculty)

@router.put("/faculty/{faculty_id}", response_model=schemas.Faculty)
async def update_faculty(
    faculty_id: int,
    faculty_update: schemas.FacultyUpdate,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(auth.require_editor)
):
    """Update faculty member"""
    db_faculty = crud.update_faculty(db, faculty_id=faculty_id, faculty_update=faculty_update)
    if db_faculty is None:
        raise HTTPException(status_code=404, detail="Faculty member not found")
    return db_faculty

@router.delete("/faculty/{faculty_id}", response_model=schemas.MessageResponse)
async def delete_faculty(
    faculty_id: int,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(auth.require_editor)
):
    """Delete faculty member"""
    success = crud.delete_faculty(db, faculty_id=faculty_id)
    if not success:
        raise HTTPException(status_code=404, detail="Faculty member not found")
    return {"message": "Faculty member deleted successfully"}

# Media file routes
@router.get("/media", response_model=List[schemas.MediaFile])
async def read_media_files(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(auth.get_current_active_user)
):
    """Get media files"""
    media_files = crud.get_media_files(db, skip=skip, limit=limit)
    return media_files

@router.post("/media/upload", response_model=schemas.MediaFile)
async def upload_media_file(
    file: UploadFile = File(...),
    db: Session = Depends(get_db),
    current_user: models.User = Depends(auth.require_editor)
):
    """Upload media file"""
    # Create uploads directory if it doesn't exist
    upload_dir = Path("uploads")
    upload_dir.mkdir(exist_ok=True)

    # Generate unique filename
    file_extension = Path(file.filename).suffix
    unique_filename = f"{uuid.uuid4()}{file_extension}"
    file_path = upload_dir / unique_filename

    # Save file
    try:
        with open(file_path, "wb") as buffer:
            content = await file.read()
            buffer.write(content)

        # Create media file record
        media_file_data = schemas.MediaFileCreate(
            filename=unique_filename,
            original_filename=file.filename,
            file_path=str(file_path),
            file_size=len(content),
            mime_type=file.content_type
        )

        return crud.create_media_file(db=db, media_file=media_file_data, uploaded_by_id=current_user.id)

    except Exception as e:
        # Clean up file if database operation fails
        if file_path.exists():
            file_path.unlink()
        raise HTTPException(status_code=500, detail=f"Failed to upload file: {str(e)}")

@router.put("/media/{file_id}", response_model=schemas.MediaFile)
async def update_media_file(
    file_id: int,
    media_update: schemas.MediaFileUpdate,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(auth.require_editor)
):
    """Update media file metadata"""
    db_media_file = crud.update_media_file(db, file_id=file_id, media_update=media_update)
    if db_media_file is None:
        raise HTTPException(status_code=404, detail="Media file not found")
    return db_media_file

@router.delete("/media/{file_id}", response_model=schemas.MessageResponse)
async def delete_media_file(
    file_id: int,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(auth.require_editor)
):
    """Delete media file"""
    # Get file info before deletion
    db_media_file = crud.get_media_file(db, file_id)
    if not db_media_file:
        raise HTTPException(status_code=404, detail="Media file not found")

    # Delete from database
    success = crud.delete_media_file(db, file_id=file_id)
    if not success:
        raise HTTPException(status_code=404, detail="Media file not found")

    # Delete physical file
    try:
        file_path = Path(db_media_file.file_path)
        if file_path.exists():
            file_path.unlink()
    except Exception as e:
        print(f"Warning: Could not delete physical file {db_media_file.file_path}: {e}")

    return {"message": "Media file deleted successfully"}

# Contact form routes
@router.get("/contact-forms", response_model=List[schemas.ContactForm])
async def read_contact_forms(
    skip: int = 0,
    limit: int = 100,
    unread_only: bool = False,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(auth.get_current_active_user)
):
    """Get contact form submissions"""
    forms = crud.get_contact_forms(db, skip=skip, limit=limit, unread_only=unread_only)
    return forms

@router.post("/contact-forms", response_model=schemas.ContactForm)
async def create_contact_form(
    contact_form: schemas.ContactFormCreate,
    request: Request,
    db: Session = Depends(get_db)
):
    """Submit contact form (public endpoint)"""
    ip_address = request.client.host
    user_agent = request.headers.get("user-agent")
    
    return crud.create_contact_form(
        db=db,
        contact_form=contact_form,
        ip_address=ip_address,
        user_agent=user_agent
    )

@router.put("/contact-forms/{form_id}/read", response_model=schemas.ContactForm)
async def mark_contact_form_read(
    form_id: int,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(auth.get_current_active_user)
):
    """Mark contact form as read"""
    db_form = crud.mark_contact_form_read(db, form_id=form_id)
    if db_form is None:
        raise HTTPException(status_code=404, detail="Contact form not found")
    return db_form
