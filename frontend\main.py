"""
Main FastAPI application for SNPITC website frontend with CMS integration
"""
from fastapi import FastAP<PERSON>, Request, HTTPException
from fastapi.templating import Jinja2Templates
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse
import json
import os
from pathlib import Path
from frontend.utils.content_helper import get_template_helpers, get_content_block
from typing import Dict, Any, Optional

# Import CMS components
from cms.database import create_tables, get_db
from cms.auth import create_default_admin
from cms.api import router as cms_router
from cms import models

# Initialize FastAPI app
app = FastAPI(title="SNPITC Website", description="S.N. Pvt. Industrial Training Institute")

# Include CMS API routes
app.include_router(cms_router)

# Setup templates and static files with content helpers
templates = Jinja2Templates(directory="frontend/templates")
templates.env.globals.update(get_template_helpers())
app.mount("/static", StaticFiles(directory="frontend/static"), name="static")
app.mount("/assets", StaticFiles(directory="scraped_data/assets"), name="assets")

# Create uploads directory if it doesn't exist
uploads_dir = Path("uploads")
uploads_dir.mkdir(exist_ok=True)
app.mount("/uploads", StaticFiles(directory="uploads"), name="uploads")

# Initialize database and create default admin
@app.on_event("startup")
async def startup_event():
    """Initialize database on startup"""
    try:
        create_tables()
        # Create default admin user
        db = next(get_db())
        create_default_admin(db)
        db.close()
        print("✅ Database initialized successfully")
    except Exception as e:
        print(f"❌ Database initialization failed: {e}")
        raise

# Load scraped data
def load_scraped_data() -> Dict[str, Any]:
    """Load scraped website data"""
    try:
        with open("scraped_data/json/content_data.json", "r", encoding="utf-8") as f:
            content_data = json.load(f)
        with open("scraped_data/json/site_structure.json", "r", encoding="utf-8") as f:
            site_structure = json.load(f)
        return {"content": content_data, "structure": site_structure}
    except FileNotFoundError:
        return {"content": {}, "structure": {}}

# Global data
scraped_data = load_scraped_data()

def get_page_content(page_url: str) -> Optional[Dict[str, Any]]:
    """Get content for a specific page"""
    base_url = "http://snpitc.in/"
    full_url = f"{base_url}{page_url}" if page_url else base_url
    return scraped_data["content"].get(full_url)

def get_navigation_data() -> Dict[str, Any]:
    """Get navigation structure based on scraped data"""
    return {
        "main_menu": [
            {"name": "Home", "url": "/", "active": False},
            {
                "name": "About Us",
                "url": "#",
                "dropdown": [
                    {"name": "About Institute", "url": "/about-institute"},
                    {"name": "Introduction of Institute", "url": "/introduction-institute"},
                    {"name": "Scheme Running in The Institute", "url": "/scheme-running"}
                ]
            },
            {
                "name": "Admissions",
                "url": "#",
                "dropdown": [
                    {"name": "Admission Criteria", "url": "/admission-criteria"},
                    {"name": "Trades Affiliated To NCVT and SCVT", "url": "/trades-ncvt-scvt"},
                    {"name": "Summary of Trades Affiliated To NCVT", "url": "/trades-ncvt"},
                    {"name": "Summary of Trades Affiliated To SCVT", "url": "/trades-scvt"},
                    {"name": "Application Format", "url": "/application-format"},
                    {"name": "Fee Structure", "url": "/fee-structure"}
                ]
            },
            {
                "name": "Facilities",
                "url": "#",
                "dropdown": [
                    {"name": "Infrastructure, Building and Workshop", "url": "/infrastructure"},
                    {"name": "Trade Specific Infrastructure", "url": "/trade-specific-infrastructure"},
                    {"name": "Electric Power Supply", "url": "/electric-power"},
                    {"name": "Library", "url": "/library"},
                    {"name": "Computer lab", "url": "/computer-lab"},
                    {"name": "Sports", "url": "/sports"}
                ]
            },
            {
                "name": "Trainee",
                "url": "#",
                "dropdown": [
                    {"name": "Achievements By Trainees", "url": "/achievements-trainees"},
                    {"name": "Records of Trainees", "url": "/records-trainees"},
                    {"name": "Attendance of Trainees", "url": "/attendance-trainees"},
                    {"name": "Certificates Issued To Trainees", "url": "/certificates-issued"},
                    {"name": "PROGRESS CARD", "url": "/progress-card"},
                    {"name": "Placements", "url": "/placements"},
                    {"name": "Results", "url": "/results"},
                    {"name": "Energy Consumption", "url": "/energy-consumption"},
                    {"name": "Raw Material Consumption", "url": "/raw-material-consumption"}
                ]
            },
            {
                "name": "Staff",
                "url": "#",
                "dropdown": [
                    {"name": "Faculty", "url": "/faculty"},
                    {"name": "Administrative Staff", "url": "/administrative-staff"},
                    {"name": "Attendance of Instructor", "url": "/attendance-instructor"}
                ]
            },
            {
                "name": "More",
                "url": "#",
                "dropdown": [
                    {"name": "Industry Institute linkage", "url": "/industry-linkage"},
                    {"name": "Activities", "url": "/activities"},
                    {"name": "RTI", "url": "/rti"},
                    {"name": "Inspection Details", "url": "/inspection-details"},
                    {"name": "State Directorate", "url": "/state-directorate"},
                    {"name": "Certificate ISO", "url": "/certificate-iso"},
                    {"name": "Funds Status", "url": "/funds-status"},
                    {"name": "DGET And State Govt. Orders", "url": "/dget-orders"},
                    {"name": "Rating Of Institute", "url": "/rating-institute"},
                    {"name": "Grievance Redressal Mechanism", "url": "/grievance-redressal"},
                    {"name": "Maintenance Expenditure", "url": "/maintenance-expenditure"}
                ]
            },
            {"name": "Gallery", "url": "/gallery"},
            {"name": "Feedback", "url": "/feedback"},
            {"name": "Contact", "url": "/contact"},
            {"name": "Site Map", "url": "/site-map"}
        ]
    }

@app.get("/", response_class=HTMLResponse)
async def home(request: Request):
    """Homepage"""
    page_content = get_page_content("")
    context = {
        "request": request,
        "title": "S.N. Pvt. Industrial Training Institute",
        "page_content": page_content,
        "navigation": get_navigation_data(),
        "current_page": "home"
    }
    return templates.TemplateResponse("pages/home.html", context)

# Removed specific routes - now handled by catch-all route

@app.get("/contact", response_class=HTMLResponse)
async def contact(request: Request):
    """Contact page"""
    page_content = get_page_content("contact.aspx")
    context = {
        "request": request,
        "title": "Contact Us - SNPITC",
        "page_content": page_content,
        "navigation": get_navigation_data(),
        "current_page": "contact"
    }
    return templates.TemplateResponse("pages/contact.html", context)

@app.get("/gallery", response_class=HTMLResponse)
async def gallery(request: Request):
    """Gallery page"""
    page_content = get_page_content("gallery.aspx")
    context = {
        "request": request,
        "title": "Gallery - SNPITC",
        "page_content": page_content,
        "navigation": get_navigation_data(),
        "current_page": "gallery"
    }
    return templates.TemplateResponse("pages/gallery.html", context)

# Admin routes (must be before catch-all route)
@app.get("/admin/login", response_class=HTMLResponse)
async def admin_login(request: Request):
    """Admin login page"""
    return templates.TemplateResponse("admin/login.html", {"request": request})

@app.get("/admin", response_class=HTMLResponse)
async def admin_dashboard(request: Request):
    """Admin dashboard"""
    context = {
        "request": request,
        "title": "Admin Dashboard - SNPITC CMS"
    }
    return templates.TemplateResponse("admin/dashboard.html", context)

@app.get("/admin/pages", response_class=HTMLResponse)
async def admin_pages(request: Request):
    """Admin pages management"""
    context = {
        "request": request,
        "title": "Pages Management - SNPITC CMS"
    }
    return templates.TemplateResponse("admin/pages.html", context)

@app.get("/admin/users", response_class=HTMLResponse)
async def admin_users(request: Request):
    """Admin users management"""
    context = {
        "request": request,
        "title": "Users Management - SNPITC CMS"
    }
    return templates.TemplateResponse("admin/users.html", context)

@app.get("/admin/faculty", response_class=HTMLResponse)
async def admin_faculty(request: Request):
    """Admin faculty management"""
    context = {
        "request": request,
        "title": "Faculty Management - SNPITC CMS"
    }
    return templates.TemplateResponse("admin/faculty.html", context)

@app.get("/admin/contact-forms", response_class=HTMLResponse)
async def admin_contact_forms(request: Request):
    """Admin contact forms management"""
    context = {
        "request": request,
        "title": "Contact Forms - SNPITC CMS"
    }
    return templates.TemplateResponse("admin/contact-forms.html", context)

@app.get("/admin/settings", response_class=HTMLResponse)
async def admin_settings(request: Request):
    """Admin site settings"""
    context = {
        "request": request,
        "title": "Site Settings - SNPITC CMS"
    }
    return templates.TemplateResponse("admin/settings.html", context)

@app.get("/admin/media", response_class=HTMLResponse)
async def admin_media(request: Request):
    """Admin media library"""
    context = {
        "request": request,
        "title": "Media Library - SNPITC CMS"
    }
    return templates.TemplateResponse("admin/media.html", context)

@app.get("/admin/news", response_class=HTMLResponse)
async def admin_news(request: Request):
    """Admin news management"""
    context = {
        "request": request,
        "title": "News Management - SNPITC CMS"
    }
    return templates.TemplateResponse("admin/news.html", context)

@app.get("/admin/events", response_class=HTMLResponse)
async def admin_events(request: Request):
    """Admin events management"""
    context = {
        "request": request,
        "title": "Event Calendar - SNPITC CMS"
    }
    return templates.TemplateResponse("admin/events.html", context)

@app.get("/admin/content-blocks", response_class=HTMLResponse)
async def admin_content_blocks(request: Request):
    """Admin content blocks management"""
    context = {
        "request": request,
        "title": "Content Blocks - SNPITC CMS"
    }
    return templates.TemplateResponse("admin/content-blocks.html", context)

# Additional route handlers for other pages (catch-all route)
@app.get("/{page_slug}", response_class=HTMLResponse)
async def dynamic_page(request: Request, page_slug: str):
    """Handle dynamic pages"""
    # Define page content mapping with basic information
    page_content_mapping = {
        # About Us
        "about-institute": {
            "title": "About Institute",
            "content": """
            <h2>About S.N. Pvt. Industrial Training Institute</h2>
            <p>S.N. Pvt. Industrial Training Institute was established in 2009 with the vision to provide quality technical education and skill development to students. The institute is approved by the Directorate of Technical Education, Government of Rajasthan and affiliated to NCVT (DGE&T) Government of India.</p>

            <h3>Our Mission</h3>
            <p>To provide world-class technical education and training that prepares students for successful careers in industry and entrepreneurship.</p>

            <h3>Our Vision</h3>
            <p>To be recognized as an excellent organization providing world-class technical education at all levels.</p>

            <h3>Key Features</h3>
            <ul>
                <li>NCVT affiliated courses</li>
                <li>Experienced faculty</li>
                <li>Modern infrastructure</li>
                <li>Industry partnerships</li>
                <li>100% placement assistance</li>
            </ul>
            """
        },
        "introduction-institute": {
            "title": "Introduction of Institute",
            "content": """
            <h2>Introduction</h2>
            <p>S.N. Pvt. Industrial Training Institute was established in 2009. At present, the institute has a total capacity of 126 sanctioned seats in Electrician trade affiliated to NCVT.</p>

            <p>Nav Chetana Shikshan Sansthan has taken up skill development as one of its major focus areas. The institute is committed to providing quality technical education and training to prepare students for the industrial workforce.</p>

            <h3>Establishment Details</h3>
            <ul>
                <li><strong>Year of Establishment:</strong> 2009</li>
                <li><strong>Affiliation:</strong> NCVT (DGE&T) Government of India</li>
                <li><strong>Approval:</strong> Directorate of Technical Education, Govt. of Rajasthan</li>
                <li><strong>Total Capacity:</strong> 126 seats</li>
                <li><strong>Main Trade:</strong> Electrician</li>
            </ul>
            """
        },
        "scheme-running": {
            "title": "Scheme Running in The Institute",
            "content": """
            <h2>Schemes Running in The Institute</h2>
            <p>S.N. Pvt. Industrial Training Institute operates under various government schemes to provide quality technical education and skill development.</p>

            <h3>NCVT Scheme</h3>
            <p>The institute operates under the National Council for Vocational Training (NCVT) scheme, which ensures standardized curriculum and certification recognized across India.</p>

            <h3>Available Trades</h3>
            <ul>
                <li>Electrician (2 Years)</li>
                <li>Duration: 24 months</li>
                <li>Intake: 126 students per year</li>
                <li>Certification: NCVT Certificate</li>
            </ul>

            <h3>Training Methodology</h3>
            <ul>
                <li>Theoretical classes</li>
                <li>Practical workshops</li>
                <li>Industry visits</li>
                <li>On-the-job training</li>
                <li>Skill assessment</li>
            </ul>
            """
        },
        # Admissions
        "admission-criteria": {
            "title": "Admission Criteria",
            "content": get_content_block('admission_criteria_content', """
            <div class="space-y-8">
                <div class="bg-gradient-to-r from-primary-50 to-blue-50 p-6 rounded-lg border-l-4 border-primary-500">
                    <h2 class="text-2xl font-bold text-gray-800 mb-4">Admission Criteria</h2>
                    <p class="text-gray-700 leading-relaxed">Admission to S.N. Pvt. Industrial Training Institute is based on merit and eligibility criteria set by NCVT and the state government.</p>
                </div>

                <div class="grid md:grid-cols-2 gap-8">
                    <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                        <h3 class="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                            <i class="fas fa-graduation-cap text-primary-600 mr-3"></i>
                            Eligibility Criteria
                        </h3>
                        <h4 class="font-medium text-gray-700 mb-3">For Electrician Trade:</h4>
                        <ul class="space-y-2 text-gray-600">
                            <li class="flex items-start"><i class="fas fa-check text-green-500 mr-2 mt-1"></i>Minimum qualification: 10th pass from recognized board</li>
                            <li class="flex items-start"><i class="fas fa-check text-green-500 mr-2 mt-1"></i>Age limit: 14-40 years</li>
                            <li class="flex items-start"><i class="fas fa-check text-green-500 mr-2 mt-1"></i>Medical fitness certificate required</li>
                        </ul>
                    </div>

                    <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                        <h3 class="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                            <i class="fas fa-clipboard-list text-primary-600 mr-3"></i>
                            Admission Process
                        </h3>
                        <ol class="space-y-2 text-gray-600">
                            <li class="flex items-start"><span class="bg-primary-100 text-primary-800 rounded-full w-6 h-6 flex items-center justify-center text-sm font-medium mr-3 mt-0.5">1</span>Fill the application form</li>
                            <li class="flex items-start"><span class="bg-primary-100 text-primary-800 rounded-full w-6 h-6 flex items-center justify-center text-sm font-medium mr-3 mt-0.5">2</span>Submit required documents</li>
                            <li class="flex items-start"><span class="bg-primary-100 text-primary-800 rounded-full w-6 h-6 flex items-center justify-center text-sm font-medium mr-3 mt-0.5">3</span>Merit list preparation</li>
                            <li class="flex items-start"><span class="bg-primary-100 text-primary-800 rounded-full w-6 h-6 flex items-center justify-center text-sm font-medium mr-3 mt-0.5">4</span>Counseling and seat allotment</li>
                            <li class="flex items-start"><span class="bg-primary-100 text-primary-800 rounded-full w-6 h-6 flex items-center justify-center text-sm font-medium mr-3 mt-0.5">5</span>Fee payment and admission confirmation</li>
                        </ol>
                    </div>
                </div>

                <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                    <h3 class="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                        <i class="fas fa-file-alt text-primary-600 mr-3"></i>
                        Required Documents
                    </h3>
                    <div class="grid md:grid-cols-2 gap-4">
                        <ul class="space-y-2 text-gray-600">
                            <li class="flex items-start"><i class="fas fa-file text-blue-500 mr-2 mt-1"></i>10th mark sheet and certificate</li>
                            <li class="flex items-start"><i class="fas fa-file text-blue-500 mr-2 mt-1"></i>Transfer certificate</li>
                            <li class="flex items-start"><i class="fas fa-file text-blue-500 mr-2 mt-1"></i>Character certificate</li>
                            <li class="flex items-start"><i class="fas fa-file text-blue-500 mr-2 mt-1"></i>Caste certificate (if applicable)</li>
                        </ul>
                        <ul class="space-y-2 text-gray-600">
                            <li class="flex items-start"><i class="fas fa-file text-blue-500 mr-2 mt-1"></i>Income certificate (if applicable)</li>
                            <li class="flex items-start"><i class="fas fa-file text-blue-500 mr-2 mt-1"></i>Medical fitness certificate</li>
                            <li class="flex items-start"><i class="fas fa-image text-blue-500 mr-2 mt-1"></i>Passport size photographs</li>
                        </ul>
                    </div>
                </div>
            </div>
            """)
        },
        "fee-structure": {
            "title": "Fee Structure",
            "content": get_content_block('fee_structure_content', """
            <div class="space-y-8">
                <div class="bg-gradient-to-r from-green-50 to-emerald-50 p-6 rounded-lg border-l-4 border-green-500">
                    <h2 class="text-2xl font-bold text-gray-800 mb-4">Fee Structure</h2>
                    <p class="text-gray-700 leading-relaxed">The fee structure for S.N. Pvt. Industrial Training Institute is designed to be affordable while maintaining quality education standards.</p>
                </div>

                <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                    <div class="bg-gray-50 px-6 py-4 border-b border-gray-200">
                        <h3 class="text-xl font-semibold text-gray-800 flex items-center">
                            <i class="fas fa-calculator text-green-600 mr-3"></i>
                            Electrician Trade Fee Structure
                        </h3>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Particulars</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Amount (Rs.)</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">Admission Fee</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">1,000</td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">Tuition Fee (Annual)</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">12,000</td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">Examination Fee</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">500</td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">Library Fee</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">300</td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">Laboratory Fee</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">1,000</td>
                                </tr>
                                <tr class="bg-green-50 border-t-2 border-green-200">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-bold text-gray-900">Total Annual Fee</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-bold text-green-700 text-right">14,800</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <div class="grid md:grid-cols-2 gap-8">
                    <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                        <h3 class="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                            <i class="fas fa-credit-card text-blue-600 mr-3"></i>
                            Payment Options
                        </h3>
                        <ul class="space-y-3 text-gray-600">
                            <li class="flex items-start">
                                <i class="fas fa-check-circle text-green-500 mr-3 mt-1"></i>
                                <div>
                                    <span class="font-medium">Annual payment with discount</span>
                                    <p class="text-sm text-gray-500">Save money with upfront payment</p>
                                </div>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-check-circle text-green-500 mr-3 mt-1"></i>
                                <div>
                                    <span class="font-medium">Semester-wise payment</span>
                                    <p class="text-sm text-gray-500">Pay in convenient installments</p>
                                </div>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-check-circle text-green-500 mr-3 mt-1"></i>
                                <div>
                                    <span class="font-medium">Monthly installments</span>
                                    <p class="text-sm text-gray-500">With prior approval</p>
                                </div>
                            </li>
                        </ul>
                    </div>

                    <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                        <h3 class="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                            <i class="fas fa-graduation-cap text-yellow-600 mr-3"></i>
                            Scholarships Available
                        </h3>
                        <ul class="space-y-3 text-gray-600">
                            <li class="flex items-start">
                                <i class="fas fa-award text-yellow-500 mr-3 mt-1"></i>
                                <span>Merit-based scholarships</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-award text-yellow-500 mr-3 mt-1"></i>
                                <span>Government scholarships for SC/ST/OBC</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-award text-yellow-500 mr-3 mt-1"></i>
                                <span>Financial assistance for economically weaker sections</span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            """)
        },
        # Faculty page with professional design
        "faculty": {
            "title": "Faculty",
            "content": get_content_block('faculty_content', """
            <div class="space-y-8">
                <div class="bg-gradient-to-r from-purple-50 to-indigo-50 p-6 rounded-lg border-l-4 border-purple-500">
                    <h2 class="text-2xl font-bold text-gray-800 mb-4">Our Faculty</h2>
                    <p class="text-gray-700 leading-relaxed">S.N. Pvt. Industrial Training Institute has a team of experienced and qualified faculty members dedicated to providing quality technical education.</p>
                </div>

                <div class="grid md:grid-cols-2 gap-8">
                    <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                        <h3 class="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                            <i class="fas fa-user-graduate text-purple-600 mr-3"></i>
                            Faculty Qualifications
                        </h3>
                        <ul class="space-y-3 text-gray-600">
                            <li class="flex items-start">
                                <i class="fas fa-check-circle text-green-500 mr-3 mt-1"></i>
                                <span>All faculty members are qualified as per NCVT norms</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-check-circle text-green-500 mr-3 mt-1"></i>
                                <span>Regular training and skill upgradation programs</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-check-circle text-green-500 mr-3 mt-1"></i>
                                <span>Industry experience and academic expertise</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-check-circle text-green-500 mr-3 mt-1"></i>
                                <span>Commitment to student development</span>
                            </li>
                        </ul>
                    </div>

                    <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                        <h3 class="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                            <i class="fas fa-chalkboard-teacher text-purple-600 mr-3"></i>
                            Teaching Methodology
                        </h3>
                        <ul class="space-y-3 text-gray-600">
                            <li class="flex items-start">
                                <i class="fas fa-lightbulb text-yellow-500 mr-3 mt-1"></i>
                                <span>Theory and practical integration</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-lightbulb text-yellow-500 mr-3 mt-1"></i>
                                <span>Hands-on training approach</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-lightbulb text-yellow-500 mr-3 mt-1"></i>
                                <span>Industry-relevant curriculum</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-lightbulb text-yellow-500 mr-3 mt-1"></i>
                                <span>Regular assessment and feedback</span>
                            </li>
                        </ul>
                    </div>
                </div>

                <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                    <h3 class="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                        <i class="fas fa-users text-purple-600 mr-3"></i>
                        Faculty Development
                    </h3>
                    <div class="grid md:grid-cols-3 gap-6">
                        <div class="text-center p-4 bg-purple-50 rounded-lg">
                            <i class="fas fa-certificate text-purple-600 text-2xl mb-3"></i>
                            <h4 class="font-semibold text-gray-800 mb-2">Continuous Training</h4>
                            <p class="text-sm text-gray-600">Regular workshops and skill development programs</p>
                        </div>
                        <div class="text-center p-4 bg-purple-50 rounded-lg">
                            <i class="fas fa-industry text-purple-600 text-2xl mb-3"></i>
                            <h4 class="font-semibold text-gray-800 mb-2">Industry Exposure</h4>
                            <p class="text-sm text-gray-600">Regular interaction with industry professionals</p>
                        </div>
                        <div class="text-center p-4 bg-purple-50 rounded-lg">
                            <i class="fas fa-book text-purple-600 text-2xl mb-3"></i>
                            <h4 class="font-semibold text-gray-800 mb-2">Research & Development</h4>
                            <p class="text-sm text-gray-600">Encouraging innovation and research activities</p>
                        </div>
                    </div>
                </div>
            </div>
            """)
        },

        # Infrastructure page
        "infrastructure": {
            "title": "Infrastructure",
            "content": get_content_block('infrastructure_content', """
            <div class="space-y-8">
                <div class="bg-gradient-to-r from-blue-50 to-cyan-50 p-6 rounded-lg border-l-4 border-blue-500">
                    <h2 class="text-2xl font-bold text-gray-800 mb-4">Infrastructure</h2>
                    <p class="text-gray-700 leading-relaxed">Our institute boasts state-of-the-art infrastructure designed to provide the best learning environment for our students.</p>
                </div>

                <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
                        <div class="text-center">
                            <i class="fas fa-tools text-blue-600 text-3xl mb-4"></i>
                            <h3 class="text-lg font-semibold text-gray-800 mb-3">Electrical Workshops</h3>
                            <p class="text-gray-600 text-sm">Well-equipped electrical workshops with modern tools and equipment for practical training</p>
                        </div>
                    </div>

                    <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
                        <div class="text-center">
                            <i class="fas fa-chalkboard text-green-600 text-3xl mb-4"></i>
                            <h3 class="text-lg font-semibold text-gray-800 mb-3">Modern Classrooms</h3>
                            <p class="text-gray-600 text-sm">Air-conditioned classrooms with audio-visual aids and smart boards</p>
                        </div>
                    </div>

                    <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
                        <div class="text-center">
                            <i class="fas fa-book text-purple-600 text-3xl mb-4"></i>
                            <h3 class="text-lg font-semibold text-gray-800 mb-3">Library</h3>
                            <p class="text-gray-600 text-sm">Comprehensive library with technical books, journals, and digital resources</p>
                        </div>
                    </div>

                    <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
                        <div class="text-center">
                            <i class="fas fa-laptop text-indigo-600 text-3xl mb-4"></i>
                            <h3 class="text-lg font-semibold text-gray-800 mb-3">Computer Lab</h3>
                            <p class="text-gray-600 text-sm">Modern computer laboratory with high-speed internet connectivity</p>
                        </div>
                    </div>

                    <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
                        <div class="text-center">
                            <i class="fas fa-running text-orange-600 text-3xl mb-4"></i>
                            <h3 class="text-lg font-semibold text-gray-800 mb-3">Sports Facilities</h3>
                            <p class="text-gray-600 text-sm">Sports ground and recreational facilities for physical fitness</p>
                        </div>
                    </div>

                    <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
                        <div class="text-center">
                            <i class="fas fa-bed text-red-600 text-3xl mb-4"></i>
                            <h3 class="text-lg font-semibold text-gray-800 mb-3">Hostel</h3>
                            <p class="text-gray-600 text-sm">Comfortable hostel accommodation for outstation students</p>
                        </div>
                    </div>
                </div>
            </div>
            """)
        }
    }

    # Check if page exists in our mapping
    if page_slug not in page_content_mapping:
        # For unmapped pages, provide a generic template
        page_content = f"""
        <h2>{page_slug.replace('-', ' ').title()}</h2>
        <p>This page is under development. Please check back later for updated content.</p>
        <p>For immediate assistance, please contact us at:</p>
        <ul>
            <li>Phone: 01564-275628</li>
            <li>Mobile: **********</li>
            <li>Email: <EMAIL></li>
        </ul>
        """
        context = {
            "request": request,
            "title": f"{page_slug.replace('-', ' ').title()} - SNPITC",
            "page_content": page_content,
            "navigation": get_navigation_data(),
            "current_page": page_slug
        }
    else:
        page_data = page_content_mapping[page_slug]
        context = {
            "request": request,
            "title": f"{page_data['title']} - SNPITC",
            "page_content": page_data['content'],
            "navigation": get_navigation_data(),
            "current_page": page_slug
        }

    return templates.TemplateResponse("pages/generic.html", context)

if __name__ == "__main__":
    import uvicorn
    uvicorn.run("frontend.main:app", host="0.0.0.0", port=8000, reload=True)
