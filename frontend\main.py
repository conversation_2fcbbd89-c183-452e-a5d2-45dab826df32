"""
Main FastAPI application for SNPITC website frontend with CMS integration
"""
from fastapi import FastAP<PERSON>, Request, HTTPException
from fastapi.templating import Jinja2Templates
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse
import json
import os
from pathlib import Path
from typing import Dict, Any, Optional

# Import CMS components
from cms.database import create_tables, get_db
from cms.auth import create_default_admin
from cms.api import router as cms_router
from cms import models

# Initialize FastAPI app
app = FastAPI(title="SNPITC Website", description="S.N. Pvt. Industrial Training Institute")

# Include CMS API routes
app.include_router(cms_router)

# Setup templates and static files
templates = Jinja2Templates(directory="frontend/templates")
app.mount("/static", StaticFiles(directory="frontend/static"), name="static")
app.mount("/assets", StaticFiles(directory="scraped_data/assets"), name="assets")

# Create uploads directory if it doesn't exist
uploads_dir = Path("uploads")
uploads_dir.mkdir(exist_ok=True)
app.mount("/uploads", StaticFiles(directory="uploads"), name="uploads")

# Initialize database and create default admin
@app.on_event("startup")
async def startup_event():
    """Initialize database on startup"""
    try:
        create_tables()
        # Create default admin user
        db = next(get_db())
        create_default_admin(db)
        db.close()
        print("✅ Database initialized successfully")
    except Exception as e:
        print(f"❌ Database initialization failed: {e}")
        raise

# Load scraped data
def load_scraped_data() -> Dict[str, Any]:
    """Load scraped website data"""
    try:
        with open("scraped_data/json/content_data.json", "r", encoding="utf-8") as f:
            content_data = json.load(f)
        with open("scraped_data/json/site_structure.json", "r", encoding="utf-8") as f:
            site_structure = json.load(f)
        return {"content": content_data, "structure": site_structure}
    except FileNotFoundError:
        return {"content": {}, "structure": {}}

# Global data
scraped_data = load_scraped_data()

def get_page_content(page_url: str) -> Optional[Dict[str, Any]]:
    """Get content for a specific page"""
    base_url = "http://snpitc.in/"
    full_url = f"{base_url}{page_url}" if page_url else base_url
    return scraped_data["content"].get(full_url)

def get_navigation_data() -> Dict[str, Any]:
    """Get navigation structure based on scraped data"""
    return {
        "main_menu": [
            {"name": "Home", "url": "/", "active": False},
            {
                "name": "About Us",
                "url": "#",
                "dropdown": [
                    {"name": "About Institute", "url": "/about-institute"},
                    {"name": "Introduction of Institute", "url": "/introduction-institute"},
                    {"name": "Scheme Running in The Institute", "url": "/scheme-running"}
                ]
            },
            {
                "name": "Admissions",
                "url": "#",
                "dropdown": [
                    {"name": "Admission Criteria", "url": "/admission-criteria"},
                    {"name": "Trades Affiliated To NCVT and SCVT", "url": "/trades-ncvt-scvt"},
                    {"name": "Summary of Trades Affiliated To NCVT", "url": "/trades-ncvt"},
                    {"name": "Summary of Trades Affiliated To SCVT", "url": "/trades-scvt"},
                    {"name": "Application Format", "url": "/application-format"},
                    {"name": "Fee Structure", "url": "/fee-structure"}
                ]
            },
            {
                "name": "Facilities",
                "url": "#",
                "dropdown": [
                    {"name": "Infrastructure, Building and Workshop", "url": "/infrastructure"},
                    {"name": "Trade Specific Infrastructure", "url": "/trade-specific-infrastructure"},
                    {"name": "Electric Power Supply", "url": "/electric-power"},
                    {"name": "Library", "url": "/library"},
                    {"name": "Computer lab", "url": "/computer-lab"},
                    {"name": "Sports", "url": "/sports"}
                ]
            },
            {
                "name": "Trainee",
                "url": "#",
                "dropdown": [
                    {"name": "Achievements By Trainees", "url": "/achievements-trainees"},
                    {"name": "Records of Trainees", "url": "/records-trainees"},
                    {"name": "Attendance of Trainees", "url": "/attendance-trainees"},
                    {"name": "Certificates Issued To Trainees", "url": "/certificates-issued"},
                    {"name": "PROGRESS CARD", "url": "/progress-card"},
                    {"name": "Placements", "url": "/placements"},
                    {"name": "Results", "url": "/results"},
                    {"name": "Energy Consumption", "url": "/energy-consumption"},
                    {"name": "Raw Material Consumption", "url": "/raw-material-consumption"}
                ]
            },
            {
                "name": "Staff",
                "url": "#",
                "dropdown": [
                    {"name": "Faculty", "url": "/faculty"},
                    {"name": "Administrative Staff", "url": "/administrative-staff"},
                    {"name": "Attendance of Instructor", "url": "/attendance-instructor"}
                ]
            },
            {
                "name": "More",
                "url": "#",
                "dropdown": [
                    {"name": "Industry Institute linkage", "url": "/industry-linkage"},
                    {"name": "Activities", "url": "/activities"},
                    {"name": "RTI", "url": "/rti"},
                    {"name": "Inspection Details", "url": "/inspection-details"},
                    {"name": "State Directorate", "url": "/state-directorate"},
                    {"name": "Certificate ISO", "url": "/certificate-iso"},
                    {"name": "Funds Status", "url": "/funds-status"},
                    {"name": "DGET And State Govt. Orders", "url": "/dget-orders"},
                    {"name": "Rating Of Institute", "url": "/rating-institute"},
                    {"name": "Grievance Redressal Mechanism", "url": "/grievance-redressal"},
                    {"name": "Maintenance Expenditure", "url": "/maintenance-expenditure"}
                ]
            },
            {"name": "Gallery", "url": "/gallery"},
            {"name": "Feedback", "url": "/feedback"},
            {"name": "Contact", "url": "/contact"},
            {"name": "Site Map", "url": "/site-map"}
        ]
    }

@app.get("/", response_class=HTMLResponse)
async def home(request: Request):
    """Homepage"""
    page_content = get_page_content("")
    context = {
        "request": request,
        "title": "S.N. Pvt. Industrial Training Institute",
        "page_content": page_content,
        "navigation": get_navigation_data(),
        "current_page": "home"
    }
    return templates.TemplateResponse("pages/home.html", context)

# Removed specific routes - now handled by catch-all route

@app.get("/contact", response_class=HTMLResponse)
async def contact(request: Request):
    """Contact page"""
    page_content = get_page_content("contact.aspx")
    context = {
        "request": request,
        "title": "Contact Us - SNPITC",
        "page_content": page_content,
        "navigation": get_navigation_data(),
        "current_page": "contact"
    }
    return templates.TemplateResponse("pages/contact.html", context)

@app.get("/gallery", response_class=HTMLResponse)
async def gallery(request: Request):
    """Gallery page"""
    page_content = get_page_content("gallery.aspx")
    context = {
        "request": request,
        "title": "Gallery - SNPITC",
        "page_content": page_content,
        "navigation": get_navigation_data(),
        "current_page": "gallery"
    }
    return templates.TemplateResponse("pages/gallery.html", context)

# Admin routes (must be before catch-all route)
@app.get("/admin/login", response_class=HTMLResponse)
async def admin_login(request: Request):
    """Admin login page"""
    return templates.TemplateResponse("admin/login.html", {"request": request})

@app.get("/admin", response_class=HTMLResponse)
async def admin_dashboard(request: Request):
    """Admin dashboard"""
    context = {
        "request": request,
        "title": "Admin Dashboard - SNPITC CMS"
    }
    return templates.TemplateResponse("admin/dashboard.html", context)

@app.get("/admin/pages", response_class=HTMLResponse)
async def admin_pages(request: Request):
    """Admin pages management"""
    context = {
        "request": request,
        "title": "Pages Management - SNPITC CMS"
    }
    return templates.TemplateResponse("admin/pages.html", context)

@app.get("/admin/users", response_class=HTMLResponse)
async def admin_users(request: Request):
    """Admin users management"""
    context = {
        "request": request,
        "title": "Users Management - SNPITC CMS"
    }
    return templates.TemplateResponse("admin/users.html", context)

@app.get("/admin/faculty", response_class=HTMLResponse)
async def admin_faculty(request: Request):
    """Admin faculty management"""
    context = {
        "request": request,
        "title": "Faculty Management - SNPITC CMS"
    }
    return templates.TemplateResponse("admin/faculty.html", context)

@app.get("/admin/contact-forms", response_class=HTMLResponse)
async def admin_contact_forms(request: Request):
    """Admin contact forms management"""
    context = {
        "request": request,
        "title": "Contact Forms - SNPITC CMS"
    }
    return templates.TemplateResponse("admin/contact-forms.html", context)

@app.get("/admin/settings", response_class=HTMLResponse)
async def admin_settings(request: Request):
    """Admin site settings"""
    context = {
        "request": request,
        "title": "Site Settings - SNPITC CMS"
    }
    return templates.TemplateResponse("admin/settings.html", context)

@app.get("/admin/media", response_class=HTMLResponse)
async def admin_media(request: Request):
    """Admin media library"""
    context = {
        "request": request,
        "title": "Media Library - SNPITC CMS"
    }
    return templates.TemplateResponse("admin/media.html", context)

@app.get("/admin/news", response_class=HTMLResponse)
async def admin_news(request: Request):
    """Admin news management"""
    context = {
        "request": request,
        "title": "News Management - SNPITC CMS"
    }
    return templates.TemplateResponse("admin/news.html", context)

@app.get("/admin/events", response_class=HTMLResponse)
async def admin_events(request: Request):
    """Admin events management"""
    context = {
        "request": request,
        "title": "Event Calendar - SNPITC CMS"
    }
    return templates.TemplateResponse("admin/events.html", context)

# Additional route handlers for other pages (catch-all route)
@app.get("/{page_slug}", response_class=HTMLResponse)
async def dynamic_page(request: Request, page_slug: str):
    """Handle dynamic pages"""
    # Define page content mapping with basic information
    page_content_mapping = {
        # About Us
        "about-institute": {
            "title": "About Institute",
            "content": """
            <h2>About S.N. Pvt. Industrial Training Institute</h2>
            <p>S.N. Pvt. Industrial Training Institute was established in 2009 with the vision to provide quality technical education and skill development to students. The institute is approved by the Directorate of Technical Education, Government of Rajasthan and affiliated to NCVT (DGE&T) Government of India.</p>

            <h3>Our Mission</h3>
            <p>To provide world-class technical education and training that prepares students for successful careers in industry and entrepreneurship.</p>

            <h3>Our Vision</h3>
            <p>To be recognized as an excellent organization providing world-class technical education at all levels.</p>

            <h3>Key Features</h3>
            <ul>
                <li>NCVT affiliated courses</li>
                <li>Experienced faculty</li>
                <li>Modern infrastructure</li>
                <li>Industry partnerships</li>
                <li>100% placement assistance</li>
            </ul>
            """
        },
        "introduction-institute": {
            "title": "Introduction of Institute",
            "content": """
            <h2>Introduction</h2>
            <p>S.N. Pvt. Industrial Training Institute was established in 2009. At present, the institute has a total capacity of 126 sanctioned seats in Electrician trade affiliated to NCVT.</p>

            <p>Nav Chetana Shikshan Sansthan has taken up skill development as one of its major focus areas. The institute is committed to providing quality technical education and training to prepare students for the industrial workforce.</p>

            <h3>Establishment Details</h3>
            <ul>
                <li><strong>Year of Establishment:</strong> 2009</li>
                <li><strong>Affiliation:</strong> NCVT (DGE&T) Government of India</li>
                <li><strong>Approval:</strong> Directorate of Technical Education, Govt. of Rajasthan</li>
                <li><strong>Total Capacity:</strong> 126 seats</li>
                <li><strong>Main Trade:</strong> Electrician</li>
            </ul>
            """
        },
        "scheme-running": {
            "title": "Scheme Running in The Institute",
            "content": """
            <h2>Schemes Running in The Institute</h2>
            <p>S.N. Pvt. Industrial Training Institute operates under various government schemes to provide quality technical education and skill development.</p>

            <h3>NCVT Scheme</h3>
            <p>The institute operates under the National Council for Vocational Training (NCVT) scheme, which ensures standardized curriculum and certification recognized across India.</p>

            <h3>Available Trades</h3>
            <ul>
                <li>Electrician (2 Years)</li>
                <li>Duration: 24 months</li>
                <li>Intake: 126 students per year</li>
                <li>Certification: NCVT Certificate</li>
            </ul>

            <h3>Training Methodology</h3>
            <ul>
                <li>Theoretical classes</li>
                <li>Practical workshops</li>
                <li>Industry visits</li>
                <li>On-the-job training</li>
                <li>Skill assessment</li>
            </ul>
            """
        },
        # Admissions
        "admission-criteria": {
            "title": "Admission Criteria",
            "content": """
            <h2>Admission Criteria</h2>
            <p>Admission to S.N. Pvt. Industrial Training Institute is based on merit and eligibility criteria set by NCVT and the state government.</p>

            <h3>Eligibility Criteria</h3>
            <h4>For Electrician Trade:</h4>
            <ul>
                <li>Minimum qualification: 10th pass from recognized board</li>
                <li>Age limit: 14-40 years</li>
                <li>Medical fitness certificate required</li>
            </ul>

            <h3>Admission Process</h3>
            <ol>
                <li>Fill the application form</li>
                <li>Submit required documents</li>
                <li>Merit list preparation</li>
                <li>Counseling and seat allotment</li>
                <li>Fee payment and admission confirmation</li>
            </ol>

            <h3>Required Documents</h3>
            <ul>
                <li>10th mark sheet and certificate</li>
                <li>Transfer certificate</li>
                <li>Character certificate</li>
                <li>Caste certificate (if applicable)</li>
                <li>Income certificate (if applicable)</li>
                <li>Medical fitness certificate</li>
                <li>Passport size photographs</li>
            </ul>
            """
        },
        "fee-structure": {
            "title": "Fee Structure",
            "content": """
            <h2>Fee Structure</h2>
            <p>The fee structure for S.N. Pvt. Industrial Training Institute is designed to be affordable while maintaining quality education standards.</p>

            <h3>Electrician Trade Fee Structure</h3>
            <table class="table table-bordered">
                <thead>
                    <tr>
                        <th>Particulars</th>
                        <th>Amount (Rs.)</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>Admission Fee</td>
                        <td>1,000</td>
                    </tr>
                    <tr>
                        <td>Tuition Fee (Annual)</td>
                        <td>12,000</td>
                    </tr>
                    <tr>
                        <td>Examination Fee</td>
                        <td>500</td>
                    </tr>
                    <tr>
                        <td>Library Fee</td>
                        <td>300</td>
                    </tr>
                    <tr>
                        <td>Laboratory Fee</td>
                        <td>1,000</td>
                    </tr>
                    <tr>
                        <td><strong>Total Annual Fee</strong></td>
                        <td><strong>14,800</strong></td>
                    </tr>
                </tbody>
            </table>

            <h3>Payment Options</h3>
            <ul>
                <li>Annual payment with discount</li>
                <li>Semester-wise payment</li>
                <li>Monthly installments (with prior approval)</li>
            </ul>

            <h3>Scholarships Available</h3>
            <ul>
                <li>Merit-based scholarships</li>
                <li>Government scholarships for SC/ST/OBC</li>
                <li>Financial assistance for economically weaker sections</li>
            </ul>
            """
        },
        # Add more pages as needed
        "faculty": {
            "title": "Faculty",
            "content": """
            <h2>Our Faculty</h2>
            <p>S.N. Pvt. Industrial Training Institute has a team of experienced and qualified faculty members dedicated to providing quality technical education.</p>

            <h3>Faculty Qualifications</h3>
            <ul>
                <li>All faculty members are qualified as per NCVT norms</li>
                <li>Regular training and skill upgradation programs</li>
                <li>Industry experience and academic expertise</li>
                <li>Commitment to student development</li>
            </ul>

            <h3>Teaching Methodology</h3>
            <ul>
                <li>Theory and practical integration</li>
                <li>Hands-on training approach</li>
                <li>Industry-relevant curriculum</li>
                <li>Regular assessment and feedback</li>
            </ul>
            """
        }
    }

    # Check if page exists in our mapping
    if page_slug not in page_content_mapping:
        # For unmapped pages, provide a generic template
        page_content = f"""
        <h2>{page_slug.replace('-', ' ').title()}</h2>
        <p>This page is under development. Please check back later for updated content.</p>
        <p>For immediate assistance, please contact us at:</p>
        <ul>
            <li>Phone: 01564-275628</li>
            <li>Mobile: **********</li>
            <li>Email: <EMAIL></li>
        </ul>
        """
        context = {
            "request": request,
            "title": f"{page_slug.replace('-', ' ').title()} - SNPITC",
            "page_content": page_content,
            "navigation": get_navigation_data(),
            "current_page": page_slug
        }
    else:
        page_data = page_content_mapping[page_slug]
        context = {
            "request": request,
            "title": f"{page_data['title']} - SNPITC",
            "page_content": page_data['content'],
            "navigation": get_navigation_data(),
            "current_page": page_slug
        }

    return templates.TemplateResponse("pages/generic.html", context)

if __name__ == "__main__":
    import uvicorn
    uvicorn.run("frontend.main:app", host="0.0.0.0", port=8000, reload=True)
