"""
Main FastAPI application for SNPITC website frontend
"""
from fastapi import <PERSON><PERSON><PERSON>, Request, HTTPException
from fastapi.templating import Jinja2Templates
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse
import json
import os
from pathlib import Path
from typing import Dict, Any, Optional

# Initialize <PERSON><PERSON>I app
app = FastAPI(title="SNPITC Website", description="S.N. Pvt. Industrial Training Institute")

# Setup templates and static files
templates = Jinja2Templates(directory="frontend/templates")
app.mount("/static", StaticFiles(directory="frontend/static"), name="static")
app.mount("/assets", StaticFiles(directory="scraped_data/assets"), name="assets")

# Load scraped data
def load_scraped_data() -> Dict[str, Any]:
    """Load scraped website data"""
    try:
        with open("scraped_data/json/content_data.json", "r", encoding="utf-8") as f:
            content_data = json.load(f)
        with open("scraped_data/json/site_structure.json", "r", encoding="utf-8") as f:
            site_structure = json.load(f)
        return {"content": content_data, "structure": site_structure}
    except FileNotFoundError:
        return {"content": {}, "structure": {}}

# Global data
scraped_data = load_scraped_data()

def get_page_content(page_url: str) -> Optional[Dict[str, Any]]:
    """Get content for a specific page"""
    base_url = "http://snpitc.in/"
    full_url = f"{base_url}{page_url}" if page_url else base_url
    return scraped_data["content"].get(full_url)

def get_navigation_data() -> Dict[str, Any]:
    """Get navigation structure"""
    return {
        "main_menu": [
            {"name": "Home", "url": "/", "active": False},
            {
                "name": "About Us", 
                "url": "#",
                "dropdown": [
                    {"name": "About Institute", "url": "/about-institute"},
                    {"name": "Introduction", "url": "/introduction"},
                    {"name": "Schemes", "url": "/schemes"}
                ]
            },
            {
                "name": "Admissions",
                "url": "#",
                "dropdown": [
                    {"name": "Admission Criteria", "url": "/admission-criteria"},
                    {"name": "NCVT & SCVT Trades", "url": "/trades"},
                    {"name": "Application Form", "url": "/application"},
                    {"name": "Fee Structure", "url": "/fees"}
                ]
            },
            {
                "name": "Facilities",
                "url": "#",
                "dropdown": [
                    {"name": "Infrastructure", "url": "/infrastructure"},
                    {"name": "Library", "url": "/library"},
                    {"name": "Computer Lab", "url": "/computer-lab"},
                    {"name": "Sports", "url": "/sports"}
                ]
            },
            {
                "name": "Students",
                "url": "#",
                "dropdown": [
                    {"name": "Achievements", "url": "/achievements"},
                    {"name": "Records", "url": "/records"},
                    {"name": "Results", "url": "/results"},
                    {"name": "Placements", "url": "/placements"}
                ]
            },
            {
                "name": "Staff",
                "url": "#",
                "dropdown": [
                    {"name": "Faculty", "url": "/faculty"},
                    {"name": "Administrative Staff", "url": "/admin-staff"}
                ]
            },
            {"name": "Gallery", "url": "/gallery"},
            {"name": "Contact", "url": "/contact"}
        ]
    }

@app.get("/", response_class=HTMLResponse)
async def home(request: Request):
    """Homepage"""
    page_content = get_page_content("")
    context = {
        "request": request,
        "title": "S.N. Pvt. Industrial Training Institute",
        "page_content": page_content,
        "navigation": get_navigation_data(),
        "current_page": "home"
    }
    return templates.TemplateResponse("pages/home.html", context)

@app.get("/about-institute", response_class=HTMLResponse)
async def about_institute(request: Request):
    """About Institute page"""
    page_content = get_page_content("aboutinstitute.aspx")
    context = {
        "request": request,
        "title": "About Institute - SNPITC",
        "page_content": page_content,
        "navigation": get_navigation_data(),
        "current_page": "about"
    }
    return templates.TemplateResponse("pages/about.html", context)

@app.get("/introduction", response_class=HTMLResponse)
async def introduction(request: Request):
    """Introduction page"""
    page_content = get_page_content("introductioninstitute.aspx")
    context = {
        "request": request,
        "title": "Introduction - SNPITC",
        "page_content": page_content,
        "navigation": get_navigation_data(),
        "current_page": "introduction"
    }
    return templates.TemplateResponse("pages/introduction.html", context)

@app.get("/contact", response_class=HTMLResponse)
async def contact(request: Request):
    """Contact page"""
    page_content = get_page_content("contact.aspx")
    context = {
        "request": request,
        "title": "Contact Us - SNPITC",
        "page_content": page_content,
        "navigation": get_navigation_data(),
        "current_page": "contact"
    }
    return templates.TemplateResponse("pages/contact.html", context)

@app.get("/gallery", response_class=HTMLResponse)
async def gallery(request: Request):
    """Gallery page"""
    page_content = get_page_content("gallery.aspx")
    context = {
        "request": request,
        "title": "Gallery - SNPITC",
        "page_content": page_content,
        "navigation": get_navigation_data(),
        "current_page": "gallery"
    }
    return templates.TemplateResponse("pages/gallery.html", context)

# Additional route handlers for other pages
@app.get("/{page_slug}", response_class=HTMLResponse)
async def dynamic_page(request: Request, page_slug: str):
    """Handle dynamic pages"""
    # Map slugs to actual page files
    page_mapping = {
        "admission-criteria": "admissioncriteria.aspx",
        "trades": "ncvtscvtaffilated.aspx",
        "application": "applicationformat.aspx",
        "fees": "fee-structure.aspx",
        "infrastructure": "infrastructure.aspx",
        "library": "library.aspx",
        "computer-lab": "computerlab.aspx",
        "sports": "sports.aspx",
        "achievements": "achievementsByTrainees.aspx",
        "records": "recordsOfTrainees.aspx",
        "results": "results.aspx",
        "placements": "placements.aspx",
        "faculty": "faculty.aspx",
        "admin-staff": "administrativeStaff.aspx",
        "schemes": "schemerunning.aspx"
    }
    
    actual_page = page_mapping.get(page_slug)
    if not actual_page:
        raise HTTPException(status_code=404, detail="Page not found")
    
    page_content = get_page_content(actual_page)
    context = {
        "request": request,
        "title": f"{page_slug.replace('-', ' ').title()} - SNPITC",
        "page_content": page_content,
        "navigation": get_navigation_data(),
        "current_page": page_slug
    }
    return templates.TemplateResponse("pages/generic.html", context)

if __name__ == "__main__":
    import uvicorn
    uvicorn.run("frontend.main:app", host="0.0.0.0", port=8000, reload=True)
