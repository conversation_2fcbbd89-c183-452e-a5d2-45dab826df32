"""
Main FastAPI application for SNPITC website frontend with CMS integration
"""
from fastapi import FastAP<PERSON>, Request, HTTPException
from fastapi.templating import Jinja2Templates
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse
import json
import os
from pathlib import Path
from typing import Dict, Any, Optional

# Import CMS components
from cms.database import create_tables, get_db
from cms.auth import create_default_admin
from cms.api import router as cms_router
from cms import models

# Initialize FastAPI app
app = FastAPI(title="SNPITC Website", description="S.N. Pvt. Industrial Training Institute")

# Include CMS API routes
app.include_router(cms_router)

# Setup templates and static files
templates = Jinja2Templates(directory="frontend/templates")
app.mount("/static", StaticFiles(directory="frontend/static"), name="static")
app.mount("/assets", StaticFiles(directory="scraped_data/assets"), name="assets")

# Initialize database and create default admin
@app.on_event("startup")
async def startup_event():
    """Initialize database on startup"""
    create_tables()
    # Create default admin user
    db = next(get_db())
    create_default_admin(db)
    db.close()

# Load scraped data
def load_scraped_data() -> Dict[str, Any]:
    """Load scraped website data"""
    try:
        with open("scraped_data/json/content_data.json", "r", encoding="utf-8") as f:
            content_data = json.load(f)
        with open("scraped_data/json/site_structure.json", "r", encoding="utf-8") as f:
            site_structure = json.load(f)
        return {"content": content_data, "structure": site_structure}
    except FileNotFoundError:
        return {"content": {}, "structure": {}}

# Global data
scraped_data = load_scraped_data()

def get_page_content(page_url: str) -> Optional[Dict[str, Any]]:
    """Get content for a specific page"""
    base_url = "http://snpitc.in/"
    full_url = f"{base_url}{page_url}" if page_url else base_url
    return scraped_data["content"].get(full_url)

def get_navigation_data() -> Dict[str, Any]:
    """Get navigation structure based on scraped data"""
    return {
        "main_menu": [
            {"name": "Home", "url": "/", "active": False},
            {
                "name": "About Us",
                "url": "#",
                "dropdown": [
                    {"name": "About Institute", "url": "/about-institute"},
                    {"name": "Introduction of Institute", "url": "/introduction-institute"},
                    {"name": "Scheme Running in The Institute", "url": "/scheme-running"}
                ]
            },
            {
                "name": "Admissions",
                "url": "#",
                "dropdown": [
                    {"name": "Admission Criteria", "url": "/admission-criteria"},
                    {"name": "Trades Affiliated To NCVT and SCVT", "url": "/trades-ncvt-scvt"},
                    {"name": "Summary of Trades Affiliated To NCVT", "url": "/trades-ncvt"},
                    {"name": "Summary of Trades Affiliated To SCVT", "url": "/trades-scvt"},
                    {"name": "Application Format", "url": "/application-format"},
                    {"name": "Fee Structure", "url": "/fee-structure"}
                ]
            },
            {
                "name": "Facilities",
                "url": "#",
                "dropdown": [
                    {"name": "Infrastructure, Building and Workshop", "url": "/infrastructure"},
                    {"name": "Trade Specific Infrastructure", "url": "/trade-specific-infrastructure"},
                    {"name": "Electric Power Supply", "url": "/electric-power"},
                    {"name": "Library", "url": "/library"},
                    {"name": "Computer lab", "url": "/computer-lab"},
                    {"name": "Sports", "url": "/sports"}
                ]
            },
            {
                "name": "Trainee",
                "url": "#",
                "dropdown": [
                    {"name": "Achievements By Trainees", "url": "/achievements-trainees"},
                    {"name": "Records of Trainees", "url": "/records-trainees"},
                    {"name": "Attendance of Trainees", "url": "/attendance-trainees"},
                    {"name": "Certificates Issued To Trainees", "url": "/certificates-issued"},
                    {"name": "PROGRESS CARD", "url": "/progress-card"},
                    {"name": "Placements", "url": "/placements"},
                    {"name": "Results", "url": "/results"},
                    {"name": "Energy Consumption", "url": "/energy-consumption"},
                    {"name": "Raw Material Consumption", "url": "/raw-material-consumption"}
                ]
            },
            {
                "name": "Staff",
                "url": "#",
                "dropdown": [
                    {"name": "Faculty", "url": "/faculty"},
                    {"name": "Administrative Staff", "url": "/administrative-staff"},
                    {"name": "Attendance of Instructor", "url": "/attendance-instructor"}
                ]
            },
            {
                "name": "More",
                "url": "#",
                "dropdown": [
                    {"name": "Industry Institute linkage", "url": "/industry-linkage"},
                    {"name": "Activities", "url": "/activities"},
                    {"name": "RTI", "url": "/rti"},
                    {"name": "Inspection Details", "url": "/inspection-details"},
                    {"name": "State Directorate", "url": "/state-directorate"},
                    {"name": "Certificate ISO", "url": "/certificate-iso"},
                    {"name": "Funds Status", "url": "/funds-status"},
                    {"name": "DGET And State Govt. Orders", "url": "/dget-orders"},
                    {"name": "Rating Of Institute", "url": "/rating-institute"},
                    {"name": "Grievance Redressal Mechanism", "url": "/grievance-redressal"},
                    {"name": "Maintenance Expenditure", "url": "/maintenance-expenditure"}
                ]
            },
            {"name": "Gallery", "url": "/gallery"},
            {"name": "Feedback", "url": "/feedback"},
            {"name": "Contact", "url": "/contact"},
            {"name": "Site Map", "url": "/site-map"}
        ]
    }

@app.get("/", response_class=HTMLResponse)
async def home(request: Request):
    """Homepage"""
    page_content = get_page_content("")
    context = {
        "request": request,
        "title": "S.N. Pvt. Industrial Training Institute",
        "page_content": page_content,
        "navigation": get_navigation_data(),
        "current_page": "home"
    }
    return templates.TemplateResponse("pages/home.html", context)

@app.get("/about-institute", response_class=HTMLResponse)
async def about_institute(request: Request):
    """About Institute page"""
    page_content = get_page_content("aboutinstitute.aspx")
    context = {
        "request": request,
        "title": "About Institute - SNPITC",
        "page_content": page_content,
        "navigation": get_navigation_data(),
        "current_page": "about"
    }
    return templates.TemplateResponse("pages/about.html", context)

@app.get("/introduction-institute", response_class=HTMLResponse)
async def introduction_institute(request: Request):
    """Introduction of Institute page"""
    page_content = get_page_content("introductioninstitute.aspx")
    context = {
        "request": request,
        "title": "Introduction of Institute - SNPITC",
        "page_content": page_content,
        "navigation": get_navigation_data(),
        "current_page": "introduction-institute"
    }
    return templates.TemplateResponse("pages/generic.html", context)

@app.get("/feedback", response_class=HTMLResponse)
async def feedback(request: Request):
    """Feedback page"""
    page_content = get_page_content("feedback.aspx")
    context = {
        "request": request,
        "title": "Feedback - SNPITC",
        "page_content": page_content,
        "navigation": get_navigation_data(),
        "current_page": "feedback"
    }
    return templates.TemplateResponse("pages/generic.html", context)

@app.get("/contact", response_class=HTMLResponse)
async def contact(request: Request):
    """Contact page"""
    page_content = get_page_content("contact.aspx")
    context = {
        "request": request,
        "title": "Contact Us - SNPITC",
        "page_content": page_content,
        "navigation": get_navigation_data(),
        "current_page": "contact"
    }
    return templates.TemplateResponse("pages/contact.html", context)

@app.get("/gallery", response_class=HTMLResponse)
async def gallery(request: Request):
    """Gallery page"""
    page_content = get_page_content("gallery.aspx")
    context = {
        "request": request,
        "title": "Gallery - SNPITC",
        "page_content": page_content,
        "navigation": get_navigation_data(),
        "current_page": "gallery"
    }
    return templates.TemplateResponse("pages/gallery.html", context)

# Additional route handlers for other pages
@app.get("/{page_slug}", response_class=HTMLResponse)
async def dynamic_page(request: Request, page_slug: str):
    """Handle dynamic pages"""
    # Map slugs to actual page files based on scraped data
    page_mapping = {
        # About Us
        "about-institute": "aboutinstitute.aspx",
        "introduction-institute": "introductioninstitute.aspx",
        "scheme-running": "schemerunning.aspx",

        # Admissions
        "admission-criteria": "admissioncriteria.aspx",
        "trades-ncvt-scvt": "ncvtscvtaffilated.aspx",
        "trades-ncvt": "ncvtaffilated.aspx",
        "trades-scvt": "scvtaffilated.aspx",
        "application-format": "applicationformat.aspx",
        "fee-structure": "fee-structure.aspx",

        # Facilities
        "infrastructure": "infrastructure.aspx",
        "trade-specific-infrastructure": "tsinfrastructure.aspx",
        "electric-power": "electricpower.aspx",
        "library": "library.aspx",
        "computer-lab": "computerlab.aspx",
        "sports": "sports.aspx",

        # Trainee
        "achievements-trainees": "achievementsByTrainees.aspx",
        "records-trainees": "recordsOfTrainees.aspx",
        "attendance-trainees": "attendanceoftrainee.aspx",
        "certificates-issued": "certificateIssued.aspx",
        "progress-card": "progresscard.aspx",
        "placements": "placements.aspx",
        "results": "results.aspx",
        "energy-consumption": "eeconsumptionpspm.aspx",
        "raw-material-consumption": "rmconsumptionpspm.aspx",

        # Staff
        "faculty": "faculty.aspx",
        "administrative-staff": "administrativeStaff.aspx",
        "attendance-instructor": "attendanceInstructor.aspx",

        # More
        "industry-linkage": "industryLinkages.aspx",
        "activities": "activities.aspx",
        "rti": "rti.aspx",
        "inspection-details": "inspectionDetails.aspx",
        "state-directorate": "stateDirectorate.aspx",
        "certificate-iso": "ISOcertificate.aspx",
        "funds-status": "fundStatus.aspx",
        "dget-orders": "DGETorders.aspx",
        "rating-institute": "ratting.aspx",
        "grievance-redressal": "grm.aspx",
        "maintenance-expenditure": "buildingmaintenance.aspx",

        # Other pages
        "feedback": "feedback.aspx",
        "site-map": "sitemap.aspx"
    }
    
    actual_page = page_mapping.get(page_slug)
    if not actual_page:
        raise HTTPException(status_code=404, detail="Page not found")
    
    page_content = get_page_content(actual_page)
    context = {
        "request": request,
        "title": f"{page_slug.replace('-', ' ').title()} - SNPITC",
        "page_content": page_content,
        "navigation": get_navigation_data(),
        "current_page": page_slug
    }
    return templates.TemplateResponse("pages/generic.html", context)

# Admin routes
@app.get("/admin/login", response_class=HTMLResponse)
async def admin_login(request: Request):
    """Admin login page"""
    return templates.TemplateResponse("admin/login.html", {"request": request})

@app.get("/admin", response_class=HTMLResponse)
async def admin_dashboard(request: Request):
    """Admin dashboard"""
    context = {
        "request": request,
        "title": "Admin Dashboard - SNPITC CMS"
    }
    return templates.TemplateResponse("admin/dashboard.html", context)

@app.get("/admin/pages", response_class=HTMLResponse)
async def admin_pages(request: Request):
    """Admin pages management"""
    context = {
        "request": request,
        "title": "Pages Management - SNPITC CMS"
    }
    return templates.TemplateResponse("admin/pages.html", context)

@app.get("/admin/users", response_class=HTMLResponse)
async def admin_users(request: Request):
    """Admin users management"""
    context = {
        "request": request,
        "title": "Users Management - SNPITC CMS"
    }
    return templates.TemplateResponse("admin/users.html", context)

if __name__ == "__main__":
    import uvicorn
    uvicorn.run("frontend.main:app", host="0.0.0.0", port=8000, reload=True)
