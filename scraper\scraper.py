"""
Main web scraper for SNPITC website
"""
import json
import time
from pathlib import Path
from urllib.parse import urljoin, urlparse
from typing import Dict, List, Set, Any, Optional
from bs4 import BeautifulSoup

from .config import (
    BASE_URL, INITIAL_PAGES, JSON_OUTPUT_DIR, 
    IMAGES_DIR, DOCUMENTS_DIR
)
from .utils import (
    make_request, download_file, extract_links, extract_media_urls,
    extract_page_metadata, clean_text, clean_filename, get_file_hash
)


class SNPITCScraper:
    """Main scraper class for SNPITC website"""
    
    def __init__(self):
        self.base_url = BASE_URL
        self.scraped_pages = set()
        self.all_links = set()
        self.media_files = {'images': {}, 'documents': {}}
        self.site_structure = {}
        self.content_data = {}
        
    def scrape_page(self, page_url: str) -> Optional[Dict[str, Any]]:
        """Scrape a single page and extract all content"""
        print(f"Scraping: {page_url}")
        
        response = make_request(page_url)
        if not response:
            return None
        
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # Extract page metadata
        metadata = extract_page_metadata(soup)
        
        # Extract main content
        content = self.extract_content(soup)
        
        # Extract navigation structure
        navigation = self.extract_navigation(soup)
        
        # Extract links
        page_links = extract_links(soup, self.base_url)
        self.all_links.update(page_links)
        
        # Extract media URLs
        media_urls = extract_media_urls(soup, self.base_url)
        
        # Store page data (convert sets to lists for JSON serialization)
        page_data = {
            'url': page_url,
            'metadata': metadata,
            'content': content,
            'navigation': navigation,
            'links': list(page_links),
            'media_urls': {k: list(v) for k, v in media_urls.items()},
            'scraped_at': time.strftime('%Y-%m-%d %H:%M:%S')
        }
        
        return page_data
    
    def extract_content(self, soup: BeautifulSoup) -> Dict[str, Any]:
        """Extract main content from page"""
        content = {
            'text_content': '',
            'structured_content': {},
            'forms': [],
            'tables': [],
            'lists': []
        }
        
        # Remove script and style elements
        for script in soup(["script", "style"]):
            script.decompose()
        
        # Extract main text content
        main_content = soup.find('body')
        if main_content:
            content['text_content'] = clean_text(main_content.get_text())
        
        # Extract structured content
        content['structured_content'] = self.extract_structured_content(soup)
        
        # Extract forms
        content['forms'] = self.extract_forms(soup)
        
        # Extract tables
        content['tables'] = self.extract_tables(soup)
        
        # Extract lists
        content['lists'] = self.extract_lists(soup)
        
        return content
    
    def extract_structured_content(self, soup: BeautifulSoup) -> Dict[str, Any]:
        """Extract structured content sections"""
        structured = {}
        
        # Look for common content sections
        sections = soup.find_all(['div', 'section', 'article'])
        for i, section in enumerate(sections):
            if section.get('id') or section.get('class'):
                key = section.get('id') or '_'.join(section.get('class', []))
                if key:
                    structured[f"section_{i}_{key}"] = clean_text(section.get_text())
        
        return structured
    
    def extract_navigation(self, soup: BeautifulSoup) -> Dict[str, Any]:
        """Extract navigation structure"""
        navigation = {
            'main_menu': [],
            'breadcrumbs': [],
            'footer_links': []
        }
        
        # Extract main navigation
        nav_elements = soup.find_all(['nav', 'ul'])
        for nav in nav_elements:
            links = nav.find_all('a', href=True)
            nav_items = []
            for link in links:
                nav_items.append({
                    'text': clean_text(link.get_text()),
                    'url': link['href'],
                    'title': link.get('title', '')
                })
            if nav_items:
                navigation['main_menu'].extend(nav_items)
        
        return navigation
    
    def extract_forms(self, soup: BeautifulSoup) -> List[Dict[str, Any]]:
        """Extract form information"""
        forms = []
        
        for form in soup.find_all('form'):
            form_data = {
                'action': form.get('action', ''),
                'method': form.get('method', 'GET'),
                'fields': []
            }
            
            # Extract form fields
            for field in form.find_all(['input', 'textarea', 'select']):
                field_data = {
                    'type': field.get('type', field.name),
                    'name': field.get('name', ''),
                    'id': field.get('id', ''),
                    'placeholder': field.get('placeholder', ''),
                    'required': field.has_attr('required')
                }
                form_data['fields'].append(field_data)
            
            forms.append(form_data)
        
        return forms
    
    def extract_tables(self, soup: BeautifulSoup) -> List[Dict[str, Any]]:
        """Extract table data"""
        tables = []
        
        for table in soup.find_all('table'):
            table_data = {
                'headers': [],
                'rows': []
            }
            
            # Extract headers
            headers = table.find_all('th')
            if headers:
                table_data['headers'] = [clean_text(th.get_text()) for th in headers]
            
            # Extract rows
            for row in table.find_all('tr'):
                cells = row.find_all(['td', 'th'])
                if cells:
                    row_data = [clean_text(cell.get_text()) for cell in cells]
                    table_data['rows'].append(row_data)
            
            tables.append(table_data)
        
        return tables
    
    def extract_lists(self, soup: BeautifulSoup) -> List[Dict[str, Any]]:
        """Extract list data"""
        lists = []
        
        for list_elem in soup.find_all(['ul', 'ol']):
            list_data = {
                'type': list_elem.name,
                'items': []
            }
            
            for item in list_elem.find_all('li'):
                list_data['items'].append(clean_text(item.get_text()))
            
            lists.append(list_data)
        
        return lists
    
    def download_media_files(self, media_urls: Dict[str, Set[str]]) -> None:
        """Download all media files"""
        print("Downloading media files...")
        
        # Download images
        for img_url in media_urls.get('images', set()):
            parsed = urlparse(img_url)
            filename = Path(parsed.path).name
            if filename:
                clean_name = clean_filename(filename)
                save_path = IMAGES_DIR / clean_name
                downloaded_path = download_file(img_url, save_path, self.base_url)
                if downloaded_path:
                    self.media_files['images'][img_url] = downloaded_path
        
        # Download documents
        for doc_url in media_urls.get('documents', set()):
            parsed = urlparse(doc_url)
            filename = Path(parsed.path).name
            if filename:
                clean_name = clean_filename(filename)
                save_path = DOCUMENTS_DIR / clean_name
                downloaded_path = download_file(doc_url, save_path, self.base_url)
                if downloaded_path:
                    self.media_files['documents'][doc_url] = downloaded_path
    
    def scrape_all_pages(self) -> None:
        """Scrape all pages from the website"""
        print("Starting comprehensive website scraping...")
        
        # Start with initial pages
        pages_to_scrape = [urljoin(self.base_url, page) for page in INITIAL_PAGES]
        
        all_media_urls = {'images': set(), 'documents': set()}
        
        for page_url in pages_to_scrape:
            if page_url not in self.scraped_pages:
                page_data = self.scrape_page(page_url)
                if page_data:
                    self.content_data[page_url] = page_data
                    self.scraped_pages.add(page_url)
                    
                    # Collect media URLs
                    for media_type, urls in page_data['media_urls'].items():
                        all_media_urls[media_type].update(urls)
        
        # Download all media files
        self.download_media_files(all_media_urls)
        
        print(f"Scraping completed. {len(self.scraped_pages)} pages scraped.")
    
    def save_data(self) -> None:
        """Save all scraped data to JSON files"""
        print("Saving scraped data...")
        
        # Save main content data
        content_file = JSON_OUTPUT_DIR / 'content_data.json'
        with open(content_file, 'w', encoding='utf-8') as f:
            json.dump(self.content_data, f, indent=2, ensure_ascii=False)
        
        # Save site structure
        structure_file = JSON_OUTPUT_DIR / 'site_structure.json'
        site_structure = {
            'base_url': self.base_url,
            'total_pages': len(self.scraped_pages),
            'scraped_pages': list(self.scraped_pages),
            'all_links': list(self.all_links),
            'media_files': self.media_files
        }
        with open(structure_file, 'w', encoding='utf-8') as f:
            json.dump(site_structure, f, indent=2, ensure_ascii=False)
        
        print(f"Data saved to {JSON_OUTPUT_DIR}")
    
    def run(self) -> None:
        """Run the complete scraping process"""
        print("SNPITC Website Scraper Starting...")
        self.scrape_all_pages()
        self.save_data()
        print("Scraping process completed successfully!")


if __name__ == "__main__":
    scraper = SNPITCScraper()
    scraper.run()
