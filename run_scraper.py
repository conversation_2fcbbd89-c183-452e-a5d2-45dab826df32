#!/usr/bin/env python3
"""
<PERSON>ript to run the SNPITC website scraper
"""
import sys
import subprocess
from pathlib import Path

def install_requirements():
    """Install required packages"""
    print("Installing required packages...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("Requirements installed successfully!")
    except subprocess.CalledProcessError as e:
        print(f"Error installing requirements: {e}")
        return False
    return True

def main():
    """Main function to run the scraper"""
    print("SNPITC Website Scraper")
    print("=" * 50)
    
    # Check if requirements.txt exists
    if not Path("requirements.txt").exists():
        print("Error: requirements.txt not found!")
        return
    
    # Install requirements
    if not install_requirements():
        print("Failed to install requirements. Exiting.")
        return
    
    # Import and run scraper
    try:
        from scraper.scraper import SNPITCScraper
        
        scraper = SNPITCScraper()
        scraper.run()
        
        print("\nScraping completed successfully!")
        print(f"Data saved in: {Path('scraped_data').absolute()}")
        
    except ImportError as e:
        print(f"Error importing scraper: {e}")
    except Exception as e:
        print(f"Error running scraper: {e}")

if __name__ == "__main__":
    main()
