<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Admin Dashboard - SNPITC CMS{% endblock %}</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                        }
                    }
                }
            }
        }
    </script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        body { font-family: 'Inter', sans-serif; }
        .sidebar-link.active { background-color: #3b82f6; color: white; }
    </style>
    
    {% block extra_head %}{% endblock %}
</head>
<body class="bg-gray-100">
    <div class="flex h-screen">
        <!-- Sidebar -->
        <div class="w-64 bg-white shadow-lg">
            <div class="p-6 border-b">
                <h1 class="text-xl font-bold text-gray-800">SNPITC CMS</h1>
                <p class="text-sm text-gray-600">Admin Dashboard</p>
            </div>
            
            <nav class="mt-6">
                <div class="px-6 py-2">
                    <p class="text-xs font-semibold text-gray-400 uppercase tracking-wider">Main</p>
                </div>
                <a href="/admin" class="sidebar-link flex items-center px-6 py-3 text-gray-700 hover:bg-gray-100 transition-colors">
                    <i class="fas fa-tachometer-alt mr-3"></i>
                    Dashboard
                </a>
                
                <div class="px-6 py-2 mt-4">
                    <p class="text-xs font-semibold text-gray-400 uppercase tracking-wider">Content</p>
                </div>
                <a href="/admin/pages" class="sidebar-link flex items-center px-6 py-3 text-gray-700 hover:bg-gray-100 transition-colors">
                    <i class="fas fa-file-alt mr-3"></i>
                    Pages
                </a>
                <a href="/admin/menu" class="sidebar-link flex items-center px-6 py-3 text-gray-700 hover:bg-gray-100 transition-colors">
                    <i class="fas fa-bars mr-3"></i>
                    Navigation Menu
                </a>
                <a href="/admin/media" class="sidebar-link flex items-center px-6 py-3 text-gray-700 hover:bg-gray-100 transition-colors">
                    <i class="fas fa-images mr-3"></i>
                    Media Library
                </a>
                <a href="/admin/news" class="sidebar-link flex items-center px-6 py-3 text-gray-700 hover:bg-gray-100 transition-colors">
                    <i class="fas fa-newspaper mr-3"></i>
                    News & Updates
                </a>
                
                <div class="px-6 py-2 mt-4">
                    <p class="text-xs font-semibold text-gray-400 uppercase tracking-wider">People</p>
                </div>
                <a href="/admin/faculty" class="sidebar-link flex items-center px-6 py-3 text-gray-700 hover:bg-gray-100 transition-colors">
                    <i class="fas fa-chalkboard-teacher mr-3"></i>
                    Faculty
                </a>
                <a href="/admin/users" class="sidebar-link flex items-center px-6 py-3 text-gray-700 hover:bg-gray-100 transition-colors">
                    <i class="fas fa-users mr-3"></i>
                    Users
                </a>
                
                <div class="px-6 py-2 mt-4">
                    <p class="text-xs font-semibold text-gray-400 uppercase tracking-wider">Communication</p>
                </div>
                <a href="/admin/contact-forms" class="sidebar-link flex items-center px-6 py-3 text-gray-700 hover:bg-gray-100 transition-colors">
                    <i class="fas fa-envelope mr-3"></i>
                    Contact Forms
                </a>
                
                <div class="px-6 py-2 mt-4">
                    <p class="text-xs font-semibold text-gray-400 uppercase tracking-wider">Settings</p>
                </div>
                <a href="/admin/settings" class="sidebar-link flex items-center px-6 py-3 text-gray-700 hover:bg-gray-100 transition-colors">
                    <i class="fas fa-cog mr-3"></i>
                    Site Settings
                </a>
            </nav>
        </div>
        
        <!-- Main Content -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Top Bar -->
            <header class="bg-white shadow-sm border-b">
                <div class="flex items-center justify-between px-6 py-4">
                    <div>
                        <h2 class="text-xl font-semibold text-gray-800">{% block page_title %}Dashboard{% endblock %}</h2>
                        <p class="text-sm text-gray-600">{% block page_description %}Manage your website content{% endblock %}</p>
                    </div>
                    
                    <div class="flex items-center space-x-4">
                        <!-- View Site Button -->
                        <a href="/" target="_blank" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors text-sm">
                            <i class="fas fa-external-link-alt mr-2"></i>View Site
                        </a>
                        
                        <!-- User Menu -->
                        <div class="relative">
                            <button id="user-menu-button" class="flex items-center text-gray-700 hover:text-gray-900 transition-colors">
                                <div class="w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center text-white text-sm font-medium mr-2">
                                    A
                                </div>
                                <span class="text-sm font-medium">Admin</span>
                                <i class="fas fa-chevron-down ml-2 text-xs"></i>
                            </button>
                            
                            <div id="user-menu" class="hidden absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border py-2 z-50">
                                <a href="/admin/profile" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                    <i class="fas fa-user mr-2"></i>Profile
                                </a>
                                <a href="/admin/change-password" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                    <i class="fas fa-key mr-2"></i>Change Password
                                </a>
                                <hr class="my-2">
                                <button onclick="logout()" class="block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-gray-100">
                                    <i class="fas fa-sign-out-alt mr-2"></i>Logout
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </header>
            
            <!-- Page Content -->
            <main class="flex-1 overflow-y-auto p-6">
                {% block content %}{% endblock %}
            </main>
        </div>
    </div>
    
    <!-- Toast Notifications -->
    <div id="toast-container" class="fixed top-4 right-4 z-50 space-y-2"></div>
    
    <!-- Loading Overlay -->
    <div id="loading-overlay" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
        <div class="bg-white rounded-lg p-6 flex items-center">
            <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-primary-600 mr-3"></div>
            <span>Loading...</span>
        </div>
    </div>
    
    <!-- JavaScript -->
    <script>
        // User menu toggle
        document.getElementById('user-menu-button').addEventListener('click', function() {
            document.getElementById('user-menu').classList.toggle('hidden');
        });
        
        // Close user menu when clicking outside
        document.addEventListener('click', function(event) {
            const userMenu = document.getElementById('user-menu');
            const userMenuButton = document.getElementById('user-menu-button');
            
            if (!userMenuButton.contains(event.target)) {
                userMenu.classList.add('hidden');
            }
        });
        
        // Active sidebar link
        const currentPath = window.location.pathname;
        const sidebarLinks = document.querySelectorAll('.sidebar-link');
        sidebarLinks.forEach(link => {
            if (link.getAttribute('href') === currentPath) {
                link.classList.add('active');
            }
        });
        
        // Toast notification function
        function showToast(message, type = 'info') {
            const toast = document.createElement('div');
            toast.className = `p-4 rounded-lg shadow-lg text-white transform translate-x-full transition-transform duration-300`;
            
            const bgColor = type === 'error' ? 'bg-red-500' : type === 'success' ? 'bg-green-500' : 'bg-blue-500';
            toast.classList.add(bgColor);
            
            toast.innerHTML = `
                <div class="flex items-center">
                    <span>${message}</span>
                    <button class="ml-4 text-white hover:text-gray-200" onclick="this.parentElement.parentElement.remove()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;
            
            document.getElementById('toast-container').appendChild(toast);
            
            // Animate in
            setTimeout(() => {
                toast.classList.remove('translate-x-full');
            }, 100);
            
            // Auto remove after 5 seconds
            setTimeout(() => {
                toast.classList.add('translate-x-full');
                setTimeout(() => {
                    if (toast.parentElement) {
                        toast.remove();
                    }
                }, 300);
            }, 5000);
        }
        
        // Loading overlay functions
        function showLoading() {
            document.getElementById('loading-overlay').classList.remove('hidden');
        }
        
        function hideLoading() {
            document.getElementById('loading-overlay').classList.add('hidden');
        }
        
        // Logout function
        function logout() {
            if (confirm('Are you sure you want to logout?')) {
                localStorage.removeItem('access_token');
                window.location.href = '/admin/login';
            }
        }
        
        // Global error handler for API calls
        window.handleApiError = function(error) {
            console.error('API Error:', error);
            if (error.status === 401) {
                showToast('Session expired. Please login again.', 'error');
                setTimeout(() => {
                    window.location.href = '/admin/login';
                }, 2000);
            } else {
                showToast(error.message || 'An error occurred', 'error');
            }
        };
        
        // Make functions globally available
        window.showToast = showToast;
        window.showLoading = showLoading;
        window.hideLoading = hideLoading;
    </script>
    
    {% block extra_scripts %}{% endblock %}
</body>
</html>
