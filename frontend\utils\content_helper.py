"""
Content Helper for Dynamic Content Management
Provides functions to retrieve and render dynamic content blocks
"""

import json
from typing import Dict, Any, Optional

# Mock content blocks storage - replace with database queries
CONTENT_BLOCKS = {
    'institute_name': {
        'type': 'text',
        'content': 'S.N. Pvt. Industrial Training Institute',
        'is_active': True
    },
    'institute_tagline': {
        'type': 'text',
        'content': 'Excellence in Technical Education',
        'is_active': True
    },
    'contact_phone': {
        'type': 'text',
        'content': '01564-275628',
        'is_active': True
    },
    'contact_mobile': {
        'type': 'text',
        'content': '9414947801',
        'is_active': True
    },
    'contact_email': {
        'type': 'text',
        'content': '<EMAIL>',
        'is_active': True
    },
    'contact_address': {
        'type': 'html',
        'content': '''<p>S.N. Pvt. Industrial Training Institute<br>
        Village Srirampur, Tehsil Srirampur<br>
        District Sikar, Rajasthan - 332715</p>''',
        'is_active': True
    },
    'about_intro': {
        'type': 'html',
        'content': '''<p>S.N. Pvt. Industrial Training Institute was established in 2009 with the vision to provide quality technical education and skill development to students. The institute is approved by the Directorate of Technical Education, Government of Rajasthan and affiliated to NCVT (DGE&T) Government of India.</p>''',
        'is_active': True
    },
    'about_mission': {
        'type': 'html',
        'content': '''<p>To provide world-class technical education and training that prepares students for successful careers in industry and entrepreneurship.</p>''',
        'is_active': True
    },
    'about_vision': {
        'type': 'html',
        'content': '''<p>To be recognized as an excellent organization providing world-class technical education at all levels.</p>''',
        'is_active': True
    },
    'hero_title': {
        'type': 'text',
        'content': 'Welcome to S.N. Pvt. Industrial Training Institute',
        'is_active': True
    },
    'hero_subtitle': {
        'type': 'text',
        'content': 'Shaping Future Technicians with Quality Education',
        'is_active': True
    },
    'hero_description': {
        'type': 'html',
        'content': '''<p>Join us for comprehensive technical training in Electrician trade with NCVT certification. Build your career with hands-on experience and industry-relevant skills.</p>''',
        'is_active': True
    },
    'admission_criteria_content': {
        'type': 'html',
        'content': '''
        <h3>Eligibility Criteria</h3>
        <h4>For Electrician Trade:</h4>
        <ul>
            <li>Minimum qualification: 10th pass from recognized board</li>
            <li>Age limit: 14-40 years</li>
            <li>Medical fitness certificate required</li>
        </ul>
        
        <h3>Admission Process</h3>
        <ol>
            <li>Fill the application form</li>
            <li>Submit required documents</li>
            <li>Merit list preparation</li>
            <li>Counseling and seat allotment</li>
            <li>Fee payment and admission confirmation</li>
        </ol>
        
        <h3>Required Documents</h3>
        <ul>
            <li>10th mark sheet and certificate</li>
            <li>Transfer certificate</li>
            <li>Character certificate</li>
            <li>Caste certificate (if applicable)</li>
            <li>Income certificate (if applicable)</li>
            <li>Medical fitness certificate</li>
            <li>Passport size photographs</li>
        </ul>
        ''',
        'is_active': True
    },
    'fee_structure_content': {
        'type': 'html',
        'content': '''
        <h3>Electrician Trade Fee Structure</h3>
        <table class="table table-bordered">
            <thead>
                <tr>
                    <th>Particulars</th>
                    <th>Amount (Rs.)</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>Admission Fee</td>
                    <td>1,000</td>
                </tr>
                <tr>
                    <td>Tuition Fee (Annual)</td>
                    <td>12,000</td>
                </tr>
                <tr>
                    <td>Examination Fee</td>
                    <td>500</td>
                </tr>
                <tr>
                    <td>Library Fee</td>
                    <td>300</td>
                </tr>
                <tr>
                    <td>Laboratory Fee</td>
                    <td>1,000</td>
                </tr>
                <tr>
                    <td><strong>Total Annual Fee</strong></td>
                    <td><strong>14,800</strong></td>
                </tr>
            </tbody>
        </table>
        
        <h3>Payment Options</h3>
        <ul>
            <li>Annual payment with discount</li>
            <li>Semester-wise payment</li>
            <li>Monthly installments (with prior approval)</li>
        </ul>
        
        <h3>Scholarships Available</h3>
        <ul>
            <li>Merit-based scholarships</li>
            <li>Government scholarships for SC/ST/OBC</li>
            <li>Financial assistance for economically weaker sections</li>
        </ul>
        ''',
        'is_active': True
    },
    'faculty_content': {
        'type': 'html',
        'content': '''
        <h3>Faculty Qualifications</h3>
        <ul>
            <li>All faculty members are qualified as per NCVT norms</li>
            <li>Regular training and skill upgradation programs</li>
            <li>Industry experience and academic expertise</li>
            <li>Commitment to student development</li>
        </ul>
        
        <h3>Teaching Methodology</h3>
        <ul>
            <li>Theory and practical integration</li>
            <li>Hands-on training approach</li>
            <li>Industry-relevant curriculum</li>
            <li>Regular assessment and feedback</li>
        </ul>
        ''',
        'is_active': True
    },
    'infrastructure_content': {
        'type': 'html',
        'content': '''
        <h3>Modern Infrastructure</h3>
        <p>Our institute boasts state-of-the-art infrastructure designed to provide the best learning environment for our students.</p>
        
        <h4>Key Facilities:</h4>
        <ul>
            <li>Well-equipped electrical workshops</li>
            <li>Modern classrooms with audio-visual aids</li>
            <li>Comprehensive library with technical books</li>
            <li>Computer laboratory with internet facility</li>
            <li>Sports and recreational facilities</li>
            <li>Hostel accommodation for outstation students</li>
        </ul>
        ''',
        'is_active': True
    }
}

def get_content_block(key: str, default: str = "") -> str:
    """
    Retrieve content block by key
    
    Args:
        key: The unique identifier for the content block
        default: Default value if block not found or inactive
        
    Returns:
        The content string or default value
    """
    block = CONTENT_BLOCKS.get(key)
    
    if not block or not block.get('is_active', True):
        return default
    
    return block.get('content', default)

def get_content_blocks_by_section(section: str) -> Dict[str, Any]:
    """
    Retrieve all content blocks for a specific section
    
    Args:
        section: The section name (e.g., 'homepage', 'about', 'contact')
        
    Returns:
        Dictionary of content blocks for the section
    """
    # In a real implementation, this would query the database
    # For now, return all blocks (mock implementation)
    return {key: block['content'] for key, block in CONTENT_BLOCKS.items() 
            if block.get('is_active', True)}

def render_content_block(key: str, default: str = "", escape_html: bool = False) -> str:
    """
    Render content block with optional HTML escaping
    
    Args:
        key: The unique identifier for the content block
        default: Default value if block not found
        escape_html: Whether to escape HTML content
        
    Returns:
        Rendered content string
    """
    content = get_content_block(key, default)
    
    if escape_html:
        import html
        content = html.escape(content)
    
    return content

def get_contact_info() -> Dict[str, str]:
    """
    Get all contact information as a dictionary
    
    Returns:
        Dictionary with contact information
    """
    return {
        'phone': get_content_block('contact_phone'),
        'mobile': get_content_block('contact_mobile'),
        'email': get_content_block('contact_email'),
        'address': get_content_block('contact_address')
    }

def get_institute_info() -> Dict[str, str]:
    """
    Get basic institute information
    
    Returns:
        Dictionary with institute information
    """
    return {
        'name': get_content_block('institute_name'),
        'tagline': get_content_block('institute_tagline'),
        'about_intro': get_content_block('about_intro'),
        'mission': get_content_block('about_mission'),
        'vision': get_content_block('about_vision')
    }

def update_content_block(key: str, content: str, content_type: str = 'text') -> bool:
    """
    Update a content block (mock implementation)
    
    Args:
        key: The unique identifier for the content block
        content: New content value
        content_type: Type of content ('text', 'html', etc.)
        
    Returns:
        True if successful, False otherwise
    """
    # In a real implementation, this would update the database
    if key in CONTENT_BLOCKS:
        CONTENT_BLOCKS[key]['content'] = content
        CONTENT_BLOCKS[key]['type'] = content_type
        return True
    else:
        # Create new block
        CONTENT_BLOCKS[key] = {
            'type': content_type,
            'content': content,
            'is_active': True
        }
        return True

# Template helper functions that can be used in Jinja2 templates
def get_template_helpers():
    """
    Return dictionary of helper functions for Jinja2 templates
    """
    return {
        'get_content': get_content_block,
        'get_contact_info': get_contact_info,
        'get_institute_info': get_institute_info,
        'render_content': render_content_block
    }
