{% extends "admin/base.html" %}

{% block page_title %}Dashboard{% endblock %}
{% block page_description %}Overview of your website statistics and recent activity{% endblock %}

{% block content %}
<!-- Stats Cards -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                <i class="fas fa-file-alt text-xl"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Total Pages</p>
                <p class="text-2xl font-semibold text-gray-900" id="total-pages">-</p>
            </div>
        </div>
    </div>
    
    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-3 rounded-full bg-green-100 text-green-600">
                <i class="fas fa-users text-xl"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Total Users</p>
                <p class="text-2xl font-semibold text-gray-900" id="total-users">-</p>
            </div>
        </div>
    </div>
    
    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-3 rounded-full bg-yellow-100 text-yellow-600">
                <i class="fas fa-chalkboard-teacher text-xl"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Faculty Members</p>
                <p class="text-2xl font-semibold text-gray-900" id="total-faculty">-</p>
            </div>
        </div>
    </div>
    
    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-3 rounded-full bg-red-100 text-red-600">
                <i class="fas fa-envelope text-xl"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Unread Messages</p>
                <p class="text-2xl font-semibold text-gray-900" id="unread-messages">-</p>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
    <div class="bg-white rounded-lg shadow p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
        <div class="grid grid-cols-2 gap-4">
            <a href="/admin/pages/new" class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <div class="p-2 bg-blue-100 rounded-lg mr-3">
                    <i class="fas fa-plus text-blue-600"></i>
                </div>
                <div>
                    <p class="font-medium text-gray-900">New Page</p>
                    <p class="text-sm text-gray-600">Create content</p>
                </div>
            </a>
            
            <a href="/admin/faculty/new" class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <div class="p-2 bg-green-100 rounded-lg mr-3">
                    <i class="fas fa-user-plus text-green-600"></i>
                </div>
                <div>
                    <p class="font-medium text-gray-900">Add Faculty</p>
                    <p class="text-sm text-gray-600">New member</p>
                </div>
            </a>
            
            <a href="/admin/news/new" class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <div class="p-2 bg-yellow-100 rounded-lg mr-3">
                    <i class="fas fa-newspaper text-yellow-600"></i>
                </div>
                <div>
                    <p class="font-medium text-gray-900">Add News</p>
                    <p class="text-sm text-gray-600">Announcement</p>
                </div>
            </a>
            
            <a href="/admin/settings" class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <div class="p-2 bg-purple-100 rounded-lg mr-3">
                    <i class="fas fa-cog text-purple-600"></i>
                </div>
                <div>
                    <p class="font-medium text-gray-900">Settings</p>
                    <p class="text-sm text-gray-600">Configure site</p>
                </div>
            </a>
        </div>
    </div>
    
    <div class="bg-white rounded-lg shadow p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">System Status</h3>
        <div class="space-y-4">
            <div class="flex items-center justify-between">
                <span class="text-sm font-medium text-gray-600">Website Status</span>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    <span class="w-2 h-2 bg-green-400 rounded-full mr-1"></span>
                    Online
                </span>
            </div>
            
            <div class="flex items-center justify-between">
                <span class="text-sm font-medium text-gray-600">Database</span>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    <span class="w-2 h-2 bg-green-400 rounded-full mr-1"></span>
                    Connected
                </span>
            </div>
            
            <div class="flex items-center justify-between">
                <span class="text-sm font-medium text-gray-600">Last Backup</span>
                <span class="text-sm text-gray-900" id="last-backup">-</span>
            </div>
            
            <div class="flex items-center justify-between">
                <span class="text-sm font-medium text-gray-600">Storage Used</span>
                <span class="text-sm text-gray-900">2.3 GB / 10 GB</span>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activity -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
    <div class="bg-white rounded-lg shadow">
        <div class="p-6 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">Recent Pages</h3>
        </div>
        <div class="p-6">
            <div id="recent-pages" class="space-y-4">
                <!-- Recent pages will be loaded here -->
                <div class="text-center text-gray-500 py-8">
                    <i class="fas fa-spinner fa-spin text-2xl mb-2"></i>
                    <p>Loading recent pages...</p>
                </div>
            </div>
        </div>
    </div>
    
    <div class="bg-white rounded-lg shadow">
        <div class="p-6 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">Recent Contact Forms</h3>
        </div>
        <div class="p-6">
            <div id="recent-contacts" class="space-y-4">
                <!-- Recent contact forms will be loaded here -->
                <div class="text-center text-gray-500 py-8">
                    <i class="fas fa-spinner fa-spin text-2xl mb-2"></i>
                    <p>Loading recent messages...</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    loadDashboardData();
});

async function loadDashboardData() {
    const token = localStorage.getItem('access_token');
    if (!token) {
        window.location.href = '/admin/login';
        return;
    }
    
    const headers = {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
    };
    
    try {
        // Load statistics
        await Promise.all([
            loadPages(),
            loadUsers(),
            loadFaculty(),
            loadContactForms()
        ]);
        
        // Set last backup date (mock data)
        document.getElementById('last-backup').textContent = new Date().toLocaleDateString();
        
    } catch (error) {
        console.error('Error loading dashboard data:', error);
        if (error.status === 401) {
            localStorage.removeItem('access_token');
            window.location.href = '/admin/login';
        }
    }
}

async function loadPages() {
    try {
        const response = await fetch('/api/cms/pages', {
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('access_token')}`
            }
        });
        
        if (response.ok) {
            const pages = await response.json();
            document.getElementById('total-pages').textContent = pages.length;
            
            // Show recent pages
            const recentPages = pages.slice(0, 5);
            const recentPagesContainer = document.getElementById('recent-pages');
            
            if (recentPages.length > 0) {
                recentPagesContainer.innerHTML = recentPages.map(page => `
                    <div class="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                        <div>
                            <p class="font-medium text-gray-900">${page.title}</p>
                            <p class="text-sm text-gray-600">${new Date(page.created_at).toLocaleDateString()}</p>
                        </div>
                        <div class="flex items-center space-x-2">
                            ${page.is_published ? 
                                '<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">Published</span>' : 
                                '<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">Draft</span>'
                            }
                            <a href="/admin/pages/${page.id}" class="text-blue-600 hover:text-blue-800">
                                <i class="fas fa-edit"></i>
                            </a>
                        </div>
                    </div>
                `).join('');
            } else {
                recentPagesContainer.innerHTML = '<p class="text-gray-500 text-center py-4">No pages found</p>';
            }
        }
    } catch (error) {
        console.error('Error loading pages:', error);
        document.getElementById('total-pages').textContent = '0';
    }
}

async function loadUsers() {
    try {
        const response = await fetch('/api/cms/users', {
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('access_token')}`
            }
        });
        
        if (response.ok) {
            const users = await response.json();
            document.getElementById('total-users').textContent = users.length;
        }
    } catch (error) {
        console.error('Error loading users:', error);
        document.getElementById('total-users').textContent = '0';
    }
}

async function loadFaculty() {
    try {
        const response = await fetch('/api/cms/faculty');
        
        if (response.ok) {
            const faculty = await response.json();
            document.getElementById('total-faculty').textContent = faculty.length;
        }
    } catch (error) {
        console.error('Error loading faculty:', error);
        document.getElementById('total-faculty').textContent = '0';
    }
}

async function loadContactForms() {
    try {
        const response = await fetch('/api/cms/contact-forms?unread_only=true', {
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('access_token')}`
            }
        });
        
        if (response.ok) {
            const forms = await response.json();
            document.getElementById('unread-messages').textContent = forms.length;
            
            // Show recent contact forms
            const recentForms = forms.slice(0, 5);
            const recentContactsContainer = document.getElementById('recent-contacts');
            
            if (recentForms.length > 0) {
                recentContactsContainer.innerHTML = recentForms.map(form => `
                    <div class="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                        <div>
                            <p class="font-medium text-gray-900">${form.name}</p>
                            <p class="text-sm text-gray-600">${form.subject || 'No subject'}</p>
                            <p class="text-xs text-gray-500">${new Date(form.created_at).toLocaleDateString()}</p>
                        </div>
                        <div class="flex items-center space-x-2">
                            ${!form.is_read ? 
                                '<span class="w-2 h-2 bg-red-500 rounded-full"></span>' : ''
                            }
                            <a href="/admin/contact-forms/${form.id}" class="text-blue-600 hover:text-blue-800">
                                <i class="fas fa-eye"></i>
                            </a>
                        </div>
                    </div>
                `).join('');
            } else {
                recentContactsContainer.innerHTML = '<p class="text-gray-500 text-center py-4">No messages found</p>';
            }
        }
    } catch (error) {
        console.error('Error loading contact forms:', error);
        document.getElementById('unread-messages').textContent = '0';
    }
}
</script>
{% endblock %}
