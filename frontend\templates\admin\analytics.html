{% extends "admin/base.html" %}

{% block page_title %}Analytics Dashboard{% endblock %}
{% block page_description %}Website traffic and usage statistics{% endblock %}

{% block extra_head %}
<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.1/dist/chart.umd.min.js"></script>
<!-- Date Range Picker -->
<script type="text/javascript" src="https://cdn.jsdelivr.net/momentjs/latest/moment.min.js"></script>
<script type="text/javascript" src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>
<link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css" />
{% endblock %}

{% block content %}
<!-- Header Actions -->
<div class="flex justify-between items-center mb-6">
    <div>
        <h3 class="text-lg font-semibold text-gray-900">Analytics Dashboard</h3>
        <p class="text-sm text-gray-600">Monitor website traffic and user engagement</p>
    </div>
    <div class="flex space-x-2">
        <div class="relative">
            <input 
                type="text" 
                id="date-range" 
                class="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                placeholder="Select date range"
            >
            <i class="fas fa-calendar-alt absolute right-3 top-3 text-gray-400"></i>
        </div>
        <button onclick="exportAnalytics()" class="bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors">
            <i class="fas fa-download mr-2"></i>Export
        </button>
    </div>
</div>

<!-- Stats Cards -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                <i class="fas fa-users text-xl"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Total Visitors</p>
                <p class="text-2xl font-semibold text-gray-900" id="total-visitors">-</p>
                <p class="text-xs text-green-600"><i class="fas fa-arrow-up mr-1"></i><span id="visitors-change">-</span>% vs. previous period</p>
            </div>
        </div>
    </div>
    
    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-3 rounded-full bg-green-100 text-green-600">
                <i class="fas fa-eye text-xl"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Page Views</p>
                <p class="text-2xl font-semibold text-gray-900" id="page-views">-</p>
                <p class="text-xs text-green-600"><i class="fas fa-arrow-up mr-1"></i><span id="pageviews-change">-</span>% vs. previous period</p>
            </div>
        </div>
    </div>
    
    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-3 rounded-full bg-yellow-100 text-yellow-600">
                <i class="fas fa-clock text-xl"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Avg. Session Duration</p>
                <p class="text-2xl font-semibold text-gray-900" id="avg-duration">-</p>
                <p class="text-xs text-red-600"><i class="fas fa-arrow-down mr-1"></i><span id="duration-change">-</span>% vs. previous period</p>
            </div>
        </div>
    </div>
    
    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-3 rounded-full bg-red-100 text-red-600">
                <i class="fas fa-percent text-xl"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Bounce Rate</p>
                <p class="text-2xl font-semibold text-gray-900" id="bounce-rate">-</p>
                <p class="text-xs text-green-600"><i class="fas fa-arrow-down mr-1"></i><span id="bounce-change">-</span>% vs. previous period</p>
            </div>
        </div>
    </div>
</div>

<!-- Charts -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
    <!-- Traffic Overview Chart -->
    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex justify-between items-center mb-6">
            <h3 class="text-lg font-semibold text-gray-900">Traffic Overview</h3>
            <div class="flex space-x-2">
                <button onclick="updateTrafficChart('day')" class="px-3 py-1 text-sm border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors traffic-period-btn active">Day</button>
                <button onclick="updateTrafficChart('week')" class="px-3 py-1 text-sm border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors traffic-period-btn">Week</button>
                <button onclick="updateTrafficChart('month')" class="px-3 py-1 text-sm border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors traffic-period-btn">Month</button>
            </div>
        </div>
        <div class="h-80">
            <canvas id="traffic-chart"></canvas>
        </div>
    </div>
    
    <!-- Top Pages Chart -->
    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex justify-between items-center mb-6">
            <h3 class="text-lg font-semibold text-gray-900">Top Pages</h3>
            <select id="top-pages-metric" class="px-3 py-1 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500">
                <option value="views">Page Views</option>
                <option value="time">Time on Page</option>
                <option value="bounce">Bounce Rate</option>
            </select>
        </div>
        <div class="h-80">
            <canvas id="pages-chart"></canvas>
        </div>
    </div>
</div>

<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
    <!-- Traffic Sources Chart -->
    <div class="bg-white rounded-lg shadow p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-6">Traffic Sources</h3>
        <div class="h-80">
            <canvas id="sources-chart"></canvas>
        </div>
    </div>
    
    <!-- Devices Chart -->
    <div class="bg-white rounded-lg shadow p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-6">Devices</h3>
        <div class="h-80">
            <canvas id="devices-chart"></canvas>
        </div>
    </div>
</div>

<!-- Detailed Stats -->
<div class="bg-white rounded-lg shadow overflow-hidden mb-8">
    <div class="p-6 border-b border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900">Detailed Statistics</h3>
    </div>
    <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Page
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Views
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Unique Visitors
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Avg. Time on Page
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Bounce Rate
                    </th>
                </tr>
            </thead>
            <tbody id="stats-table-body" class="bg-white divide-y divide-gray-200">
                <!-- Stats will be loaded here -->
                <tr>
                    <td colspan="5" class="px-6 py-12 text-center text-gray-500">
                        <i class="fas fa-spinner fa-spin text-2xl mb-2"></i>
                        <p>Loading statistics...</p>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
</div>

<!-- Real-time Visitors -->
<div class="bg-white rounded-lg shadow p-6">
    <div class="flex justify-between items-center mb-6">
        <h3 class="text-lg font-semibold text-gray-900">Real-time Visitors</h3>
        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
            <span class="w-2 h-2 bg-green-400 rounded-full mr-1"></span>
            <span id="active-users">0</span> active now
        </span>
    </div>
    <div id="realtime-visitors" class="space-y-4">
        <!-- Real-time visitor data will be loaded here -->
        <p class="text-center text-gray-500 py-4">No active visitors at the moment</p>
    </div>
</div>
{% endblock %}
