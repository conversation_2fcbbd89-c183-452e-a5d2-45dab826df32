{% extends "admin/base.html" %}

{% block page_title %}Contact Forms{% endblock %}
{% block page_description %}View and manage contact form submissions{% endblock %}

{% block content %}
<!-- Header Actions -->
<div class="flex justify-between items-center mb-6">
    <div>
        <h3 class="text-lg font-semibold text-gray-900">Contact Form Submissions</h3>
        <p class="text-sm text-gray-600">View and respond to visitor inquiries</p>
    </div>
    <div class="flex space-x-2">
        <button onclick="markAllAsRead()" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors">
            <i class="fas fa-check-double mr-2"></i>Mark All Read
        </button>
        <button onclick="exportContacts()" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
            <i class="fas fa-download mr-2"></i>Export
        </button>
    </div>
</div>

<!-- Stats Cards -->
<div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                <i class="fas fa-envelope text-xl"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Total Messages</p>
                <p class="text-2xl font-semibold text-gray-900" id="total-messages">-</p>
            </div>
        </div>
    </div>
    
    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-3 rounded-full bg-red-100 text-red-600">
                <i class="fas fa-envelope-open text-xl"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Unread Messages</p>
                <p class="text-2xl font-semibold text-gray-900" id="unread-messages">-</p>
            </div>
        </div>
    </div>
    
    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-3 rounded-full bg-green-100 text-green-600">
                <i class="fas fa-calendar text-xl"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">This Month</p>
                <p class="text-2xl font-semibold text-gray-900" id="month-messages">-</p>
            </div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="bg-white rounded-lg shadow mb-6 p-4">
    <div class="flex flex-col md:flex-row gap-4">
        <div class="flex-1">
            <input 
                type="text" 
                id="search-contacts" 
                placeholder="Search messages..." 
                class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
            >
        </div>
        <div class="flex gap-2">
            <select id="filter-status" class="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500">
                <option value="">All Messages</option>
                <option value="unread">Unread</option>
                <option value="read">Read</option>
            </select>
            <select id="filter-subject" class="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500">
                <option value="">All Subjects</option>
                <option value="admission">Admission Inquiry</option>
                <option value="course">Course Information</option>
                <option value="placement">Placement Query</option>
                <option value="general">General Information</option>
                <option value="other">Other</option>
            </select>
        </div>
    </div>
</div>

<!-- Contact Forms Table -->
<div class="bg-white rounded-lg shadow overflow-hidden">
    <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        <input type="checkbox" id="select-all" class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded">
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Contact
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Subject
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Message
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Date
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                    </th>
                </tr>
            </thead>
            <tbody id="contacts-table-body" class="bg-white divide-y divide-gray-200">
                <!-- Contact forms will be loaded here -->
                <tr>
                    <td colspan="7" class="px-6 py-12 text-center text-gray-500">
                        <i class="fas fa-spinner fa-spin text-2xl mb-2"></i>
                        <p>Loading contact forms...</p>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
</div>

<!-- Pagination -->
<div id="pagination" class="mt-6 flex items-center justify-between">
    <div class="text-sm text-gray-700">
        Showing <span id="showing-from">0</span> to <span id="showing-to">0</span> of <span id="total-contacts">0</span> messages
    </div>
    <div class="flex space-x-2">
        <button id="prev-page" class="px-3 py-2 border border-gray-300 rounded-lg text-sm hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
            Previous
        </button>
        <button id="next-page" class="px-3 py-2 border border-gray-300 rounded-lg text-sm hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
            Next
        </button>
    </div>
</div>

<!-- View Message Modal -->
<div id="message-modal" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
    <div class="bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <div class="p-6 border-b border-gray-200">
            <div class="flex justify-between items-center">
                <h3 class="text-lg font-semibold text-gray-900">Contact Message</h3>
                <button onclick="closeMessageModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
        </div>
        
        <div class="p-6">
            <div class="space-y-4">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Name</label>
                        <p id="message-name" class="text-gray-900"></p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Email</label>
                        <p id="message-email" class="text-gray-900"></p>
                    </div>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Phone</label>
                        <p id="message-phone" class="text-gray-900"></p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Subject</label>
                        <p id="message-subject" class="text-gray-900"></p>
                    </div>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Message</label>
                    <div id="message-content" class="bg-gray-50 p-4 rounded-lg text-gray-900 whitespace-pre-wrap"></div>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Submitted</label>
                        <p id="message-date"></p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">IP Address</label>
                        <p id="message-ip"></p>
                    </div>
                </div>
            </div>
            
            <div class="flex justify-between items-center pt-6 border-t border-gray-200 mt-6">
                <div class="flex space-x-2">
                    <button onclick="replyToMessage()" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                        <i class="fas fa-reply mr-2"></i>Reply
                    </button>
                    <button onclick="markAsRead()" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors">
                        <i class="fas fa-check mr-2"></i>Mark as Read
                    </button>
                </div>
                <button onclick="deleteMessage()" class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors">
                    <i class="fas fa-trash mr-2"></i>Delete
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
let currentPage = 1;
let totalPages = 0;
let currentMessageId = null;
let selectedMessages = new Set();

document.addEventListener('DOMContentLoaded', function() {
    loadContactForms();
    loadStats();
    setupEventListeners();
});

function setupEventListeners() {
    // Search and filters
    document.getElementById('search-contacts').addEventListener('input', debounce(loadContactForms, 300));
    document.getElementById('filter-status').addEventListener('change', loadContactForms);
    document.getElementById('filter-subject').addEventListener('change', loadContactForms);
    
    // Select all checkbox
    document.getElementById('select-all').addEventListener('change', function() {
        const checkboxes = document.querySelectorAll('.message-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
            if (this.checked) {
                selectedMessages.add(parseInt(checkbox.value));
            } else {
                selectedMessages.delete(parseInt(checkbox.value));
            }
        });
    });
    
    // Pagination
    document.getElementById('prev-page').addEventListener('click', () => {
        if (currentPage > 1) {
            currentPage--;
            loadContactForms();
        }
    });
    
    document.getElementById('next-page').addEventListener('click', () => {
        if (currentPage < totalPages) {
            currentPage++;
            loadContactForms();
        }
    });
}

async function loadStats() {
    try {
        const response = await fetch('/api/cms/contact-forms', {
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('access_token')}`
            }
        });
        
        if (response.ok) {
            const forms = await response.json();
            const unreadCount = forms.filter(form => !form.is_read).length;
            const thisMonth = new Date();
            thisMonth.setDate(1);
            const monthCount = forms.filter(form => new Date(form.created_at) >= thisMonth).length;
            
            document.getElementById('total-messages').textContent = forms.length;
            document.getElementById('unread-messages').textContent = unreadCount;
            document.getElementById('month-messages').textContent = monthCount;
        }
    } catch (error) {
        console.error('Error loading stats:', error);
    }
}

async function loadContactForms() {
    showLoading();
    
    const search = document.getElementById('search-contacts').value;
    const status = document.getElementById('filter-status').value;
    const subject = document.getElementById('filter-subject').value;
    
    try {
        let url = '/api/cms/contact-forms';
        if (status === 'unread') {
            url += '?unread_only=true';
        }
        
        const response = await fetch(url, {
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('access_token')}`
            }
        });
        
        if (response.ok) {
            const forms = await response.json();
            
            // Filter forms based on search and filters
            let filteredForms = forms;
            
            if (search) {
                filteredForms = filteredForms.filter(form => 
                    form.name.toLowerCase().includes(search.toLowerCase()) ||
                    form.email.toLowerCase().includes(search.toLowerCase()) ||
                    (form.subject && form.subject.toLowerCase().includes(search.toLowerCase())) ||
                    form.message.toLowerCase().includes(search.toLowerCase())
                );
            }
            
            if (status === 'read') {
                filteredForms = filteredForms.filter(form => form.is_read);
            }
            
            if (subject) {
                filteredForms = filteredForms.filter(form => form.subject === subject);
            }
            
            displayContactForms(filteredForms);
            updatePagination(filteredForms.length);
        } else {
            throw new Error('Failed to load contact forms');
        }
    } catch (error) {
        console.error('Error loading contact forms:', error);
        showToast('Error loading contact forms', 'error');
    } finally {
        hideLoading();
    }
}

function displayContactForms(forms) {
    const tbody = document.getElementById('contacts-table-body');
    
    if (forms.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="7" class="px-6 py-12 text-center text-gray-500">
                    <i class="fas fa-envelope text-4xl mb-4 text-gray-300"></i>
                    <p class="text-lg font-medium mb-2">No contact forms found</p>
                    <p class="text-sm">Contact forms will appear here when visitors submit them</p>
                </td>
            </tr>
        `;
        return;
    }
    
    tbody.innerHTML = forms.map(form => `
        <tr class="hover:bg-gray-50 ${!form.is_read ? 'bg-blue-50' : ''}">
            <td class="px-6 py-4 whitespace-nowrap">
                <input type="checkbox" class="message-checkbox h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded" value="${form.id}">
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
                <div>
                    <div class="text-sm font-medium text-gray-900 flex items-center">
                        ${!form.is_read ? '<span class="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>' : ''}
                        ${form.name}
                    </div>
                    <div class="text-sm text-gray-500">${form.email}</div>
                    ${form.phone ? `<div class="text-xs text-gray-400">${form.phone}</div>` : ''}
                </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getSubjectBadgeClass(form.subject)}">
                    ${form.subject || 'No Subject'}
                </span>
            </td>
            <td class="px-6 py-4">
                <div class="text-sm text-gray-900 max-w-xs truncate">
                    ${form.message}
                </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                ${new Date(form.created_at).toLocaleDateString()}
                <div class="text-xs text-gray-400">
                    ${new Date(form.created_at).toLocaleTimeString()}
                </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${form.is_read ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}">
                    ${form.is_read ? 'Read' : 'Unread'}
                </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <div class="flex space-x-2">
                    <button onclick="viewMessage(${form.id})" class="text-blue-600 hover:text-blue-800" title="View">
                        <i class="fas fa-eye"></i>
                    </button>
                    <a href="mailto:${form.email}" class="text-green-600 hover:text-green-800" title="Reply">
                        <i class="fas fa-reply"></i>
                    </a>
                    <button onclick="deleteContactForm(${form.id})" class="text-red-600 hover:text-red-800" title="Delete">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
    
    // Add event listeners to checkboxes
    document.querySelectorAll('.message-checkbox').forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            if (this.checked) {
                selectedMessages.add(parseInt(this.value));
            } else {
                selectedMessages.delete(parseInt(this.value));
            }
        });
    });
}

function getSubjectBadgeClass(subject) {
    switch (subject) {
        case 'admission':
            return 'bg-blue-100 text-blue-800';
        case 'course':
            return 'bg-green-100 text-green-800';
        case 'placement':
            return 'bg-yellow-100 text-yellow-800';
        case 'general':
            return 'bg-purple-100 text-purple-800';
        default:
            return 'bg-gray-100 text-gray-800';
    }
}

function updatePagination(total) {
    document.getElementById('total-contacts').textContent = total;
    document.getElementById('showing-from').textContent = total > 0 ? 1 : 0;
    document.getElementById('showing-to').textContent = total;
}

async function viewMessage(messageId) {
    try {
        const response = await fetch(`/api/cms/contact-forms`, {
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('access_token')}`
            }
        });
        
        if (response.ok) {
            const forms = await response.json();
            const form = forms.find(f => f.id === messageId);
            
            if (form) {
                currentMessageId = messageId;
                
                // Populate modal
                document.getElementById('message-name').textContent = form.name;
                document.getElementById('message-email').textContent = form.email;
                document.getElementById('message-phone').textContent = form.phone || 'Not provided';
                document.getElementById('message-subject').textContent = form.subject || 'No subject';
                document.getElementById('message-content').textContent = form.message;
                document.getElementById('message-date').textContent = new Date(form.created_at).toLocaleString();
                document.getElementById('message-ip').textContent = form.ip_address || 'Unknown';
                
                document.getElementById('message-modal').classList.remove('hidden');
                
                // Mark as read if not already
                if (!form.is_read) {
                    await markMessageAsRead(messageId);
                }
            }
        }
    } catch (error) {
        console.error('Error loading message:', error);
        showToast('Error loading message', 'error');
    }
}

function closeMessageModal() {
    document.getElementById('message-modal').classList.add('hidden');
    currentMessageId = null;
}

async function markMessageAsRead(messageId) {
    try {
        const response = await fetch(`/api/cms/contact-forms/${messageId}/read`, {
            method: 'PUT',
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('access_token')}`
            }
        });
        
        if (response.ok) {
            loadContactForms();
            loadStats();
        }
    } catch (error) {
        console.error('Error marking message as read:', error);
    }
}

function markAsRead() {
    if (currentMessageId) {
        markMessageAsRead(currentMessageId);
        closeMessageModal();
    }
}

function replyToMessage() {
    const email = document.getElementById('message-email').textContent;
    const subject = document.getElementById('message-subject').textContent;
    const replySubject = subject.startsWith('Re:') ? subject : `Re: ${subject}`;
    
    window.open(`mailto:${email}?subject=${encodeURIComponent(replySubject)}`);
}

async function deleteMessage() {
    if (!currentMessageId) return;
    
    if (confirm('Are you sure you want to delete this message?')) {
        await deleteContactForm(currentMessageId);
        closeMessageModal();
    }
}

async function deleteContactForm(formId) {
    try {
        showLoading();
        
        // Note: This endpoint doesn't exist in our API, but we'll simulate it
        showToast('Delete functionality not implemented yet', 'info');
        
    } catch (error) {
        console.error('Error deleting contact form:', error);
        showToast('Error deleting contact form', 'error');
    } finally {
        hideLoading();
    }
}

async function markAllAsRead() {
    if (confirm('Mark all messages as read?')) {
        try {
            showLoading();
            showToast('Mark all as read functionality not implemented yet', 'info');
        } catch (error) {
            console.error('Error marking all as read:', error);
            showToast('Error marking all as read', 'error');
        } finally {
            hideLoading();
        }
    }
}

function exportContacts() {
    showToast('Export functionality not implemented yet', 'info');
}

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}
</script>
{% endblock %}
