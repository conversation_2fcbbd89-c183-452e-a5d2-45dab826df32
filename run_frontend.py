#!/usr/bin/env python3
"""
Script to run the SNPITC website frontend
"""
import uvicorn
import sys
from pathlib import Path

def main():
    """Main function to run the frontend server"""
    print("SNPITC Website Frontend Server")
    print("=" * 50)
    
    try:
        # Run the FastAPI application
        uvicorn.run(
            "frontend.main:app",
            host="0.0.0.0",
            port=8000,
            reload=True,
            log_level="info"
        )
    except KeyboardInterrupt:
        print("\nServer stopped by user")
    except Exception as e:
        print(f"Error running server: {e}")

if __name__ == "__main__":
    main()
