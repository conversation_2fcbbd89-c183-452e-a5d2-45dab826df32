"""
Configuration settings for the web scraper
"""
import os
from pathlib import Path

# Base configuration
BASE_URL = "http://snpitc.in/"
PROJECT_ROOT = Path(__file__).parent.parent
SCRAPED_DATA_DIR = PROJECT_ROOT / "scraped_data"
ASSETS_DIR = SCRAPED_DATA_DIR / "assets"
IMAGES_DIR = ASSETS_DIR / "images"
DOCUMENTS_DIR = ASSETS_DIR / "documents"
JSON_OUTPUT_DIR = SCRAPED_DATA_DIR / "json"

# Create directories if they don't exist
for directory in [SCRAPED_DATA_DIR, ASSETS_DIR, IMAGES_DIR, DOCUMENTS_DIR, JSON_OUTPUT_DIR]:
    directory.mkdir(parents=True, exist_ok=True)

# Scraping settings
REQUEST_DELAY = 1  # Delay between requests in seconds
MAX_RETRIES = 3
TIMEOUT = 30

# Headers to mimic a real browser
DEFAULT_HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
    'Accept-Language': 'en-US,en;q=0.5',
    'Accept-Encoding': 'gzip, deflate',
    'Connection': 'keep-alive',
    'Upgrade-Insecure-Requests': '1',
}

# File extensions to download
IMAGE_EXTENSIONS = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.svg', '.webp'}
DOCUMENT_EXTENSIONS = {'.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.txt'}

# Pages to scrape (will be discovered dynamically)
INITIAL_PAGES = [
    "",  # Homepage
    "aboutinstitute.aspx",
    "introductioninstitute.aspx",
    "schemerunning.aspx",
    "admissioncriteria.aspx",
    "ncvtscvtaffilated.aspx",
    "ncvtaffilated.aspx",
    "scvtaffilated.aspx",
    "applicationformat.aspx",
    "fee-structure.aspx",
    "infrastructure.aspx",
    "tsinfrastructure.aspx",
    "electricpower.aspx",
    "library.aspx",
    "computerlab.aspx",
    "sports.aspx",
    "achievementsByTrainees.aspx",
    "recordsOfTrainees.aspx",
    "attendanceoftrainee.aspx",
    "certificateIssued.aspx",
    "progresscard.aspx",
    "placements.aspx",
    "results.aspx",
    "eeconsumptionpspm.aspx",
    "rmconsumptionpspm.aspx",
    "faculty.aspx",
    "administrativeStaff.aspx",
    "attendanceInstructor.aspx",
    "industryLinkages.aspx",
    "industryPartner.aspx",
    "MajorActivities.aspx",
    "industyVisit.aspx",
    "guestFaculty.aspx",
    "workshopSeminar.aspx",
    "activities.aspx",
    "rti.aspx",
    "inspectionDetails.aspx",
    "stateDirectorate.aspx",
    "ISOcertificate.aspx",
    "fundStatus.aspx",
    "DGETorders.aspx",
    "ratting.aspx",
    "grm.aspx",
    "buildingmaintenance.aspx",
    "gallery.aspx",
    "feedback.aspx",
    "contact.aspx",
    "sitemap.aspx"
]
