{% extends "admin/base.html" %}

{% block page_title %}Users Management{% endblock %}
{% block page_description %}Manage user accounts and permissions{% endblock %}

{% block content %}
<!-- Header Actions -->
<div class="flex justify-between items-center mb-6">
    <div>
        <h3 class="text-lg font-semibold text-gray-900">All Users</h3>
        <p class="text-sm text-gray-600">Manage user accounts, roles, and permissions</p>
    </div>
    <button onclick="openCreateUserModal()" class="bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors">
        <i class="fas fa-user-plus mr-2"></i>New User
    </button>
</div>

<!-- Filters -->
<div class="bg-white rounded-lg shadow mb-6 p-4">
    <div class="flex flex-col md:flex-row gap-4">
        <div class="flex-1">
            <input 
                type="text" 
                id="search-users" 
                placeholder="Search users..." 
                class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
            >
        </div>
        <div class="flex gap-2">
            <select id="filter-role" class="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500">
                <option value="">All Roles</option>
                <option value="admin">Admin</option>
                <option value="editor">Editor</option>
                <option value="viewer">Viewer</option>
            </select>
            <select id="filter-status" class="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500">
                <option value="">All Status</option>
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
            </select>
        </div>
    </div>
</div>

<!-- Users Table -->
<div class="bg-white rounded-lg shadow overflow-hidden">
    <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        User
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Username
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Role
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Created
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                    </th>
                </tr>
            </thead>
            <tbody id="users-table-body" class="bg-white divide-y divide-gray-200">
                <!-- Users will be loaded here -->
                <tr>
                    <td colspan="6" class="px-6 py-12 text-center text-gray-500">
                        <i class="fas fa-spinner fa-spin text-2xl mb-2"></i>
                        <p>Loading users...</p>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
</div>

<!-- Create/Edit User Modal -->
<div id="user-modal" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
    <div class="bg-white rounded-lg shadow-xl w-full max-w-2xl">
        <div class="p-6 border-b border-gray-200">
            <div class="flex justify-between items-center">
                <h3 id="user-modal-title" class="text-lg font-semibold text-gray-900">Create New User</h3>
                <button onclick="closeUserModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
        </div>
        
        <form id="user-form" class="p-6 space-y-6">
            <input type="hidden" id="user-id" name="id">
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="user-full-name" class="block text-sm font-medium text-gray-700 mb-2">Full Name *</label>
                    <input 
                        type="text" 
                        id="user-full-name" 
                        name="full_name" 
                        required
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                        placeholder="Enter full name"
                    >
                </div>
                
                <div>
                    <label for="user-username" class="block text-sm font-medium text-gray-700 mb-2">Username *</label>
                    <input 
                        type="text" 
                        id="user-username" 
                        name="username" 
                        required
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                        placeholder="Enter username"
                    >
                </div>
            </div>
            
            <div>
                <label for="user-email" class="block text-sm font-medium text-gray-700 mb-2">Email Address *</label>
                <input 
                    type="email" 
                    id="user-email" 
                    name="email" 
                    required
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                    placeholder="Enter email address"
                >
            </div>
            
            <div id="password-section">
                <label for="user-password" class="block text-sm font-medium text-gray-700 mb-2">
                    Password <span id="password-required">*</span>
                </label>
                <div class="relative">
                    <input 
                        type="password" 
                        id="user-password" 
                        name="password" 
                        class="w-full px-3 py-2 pr-10 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                        placeholder="Enter password"
                    >
                    <button 
                        type="button" 
                        id="toggle-user-password"
                        class="absolute inset-y-0 right-0 pr-3 flex items-center"
                    >
                        <i class="fas fa-eye text-gray-400 hover:text-gray-600"></i>
                    </button>
                </div>
                <p class="text-xs text-gray-500 mt-1" id="password-help">Minimum 6 characters</p>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="user-role" class="block text-sm font-medium text-gray-700 mb-2">Role *</label>
                    <select 
                        id="user-role" 
                        name="role" 
                        required
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                    >
                        <option value="viewer">Viewer</option>
                        <option value="editor">Editor</option>
                        <option value="admin">Admin</option>
                    </select>
                    <p class="text-xs text-gray-500 mt-1">
                        <strong>Viewer:</strong> Read-only access<br>
                        <strong>Editor:</strong> Can create and edit content<br>
                        <strong>Admin:</strong> Full access to all features
                    </p>
                </div>
                
                <div class="flex items-center pt-6">
                    <label class="flex items-center">
                        <input 
                            type="checkbox" 
                            id="user-active" 
                            name="is_active"
                            checked
                            class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                        >
                        <span class="ml-2 text-sm text-gray-700">Active User</span>
                    </label>
                </div>
            </div>
            
            <div class="flex justify-end space-x-4 pt-6 border-t border-gray-200">
                <button type="button" onclick="closeUserModal()" class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors">
                    Cancel
                </button>
                <button type="submit" class="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors">
                    <span id="user-save-button-text">Create User</span>
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div id="delete-user-modal" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
    <div class="bg-white rounded-lg shadow-xl w-full max-w-md">
        <div class="p-6">
            <div class="flex items-center mb-4">
                <div class="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center mr-4">
                    <i class="fas fa-exclamation-triangle text-red-600"></i>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-900">Delete User</h3>
                    <p class="text-sm text-gray-600">This action cannot be undone</p>
                </div>
            </div>
            
            <p class="text-gray-700 mb-6">Are you sure you want to delete user "<span id="delete-user-name"></span>"?</p>
            
            <div class="flex justify-end space-x-4">
                <button onclick="closeDeleteUserModal()" class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors">
                    Cancel
                </button>
                <button onclick="confirmDeleteUser()" class="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors">
                    Delete User
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
let currentEditingUserId = null;
let currentDeleteUserId = null;

document.addEventListener('DOMContentLoaded', function() {
    loadUsers();
    setupEventListeners();
});

function setupEventListeners() {
    // Search and filters
    document.getElementById('search-users').addEventListener('input', debounce(loadUsers, 300));
    document.getElementById('filter-role').addEventListener('change', loadUsers);
    document.getElementById('filter-status').addEventListener('change', loadUsers);
    
    // Password toggle
    document.getElementById('toggle-user-password').addEventListener('click', function() {
        const passwordInput = document.getElementById('user-password');
        const icon = this.querySelector('i');
        
        if (passwordInput.type === 'password') {
            passwordInput.type = 'text';
            icon.classList.remove('fa-eye');
            icon.classList.add('fa-eye-slash');
        } else {
            passwordInput.type = 'password';
            icon.classList.remove('fa-eye-slash');
            icon.classList.add('fa-eye');
        }
    });
    
    // Form submission
    document.getElementById('user-form').addEventListener('submit', handleUserSubmit);
}

async function loadUsers() {
    showLoading();
    
    const search = document.getElementById('search-users').value;
    const role = document.getElementById('filter-role').value;
    const status = document.getElementById('filter-status').value;
    
    try {
        const response = await fetch('/api/cms/users', {
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('access_token')}`
            }
        });
        
        if (response.ok) {
            const users = await response.json();
            
            // Filter users based on search and filters
            let filteredUsers = users;
            
            if (search) {
                filteredUsers = filteredUsers.filter(user => 
                    user.full_name.toLowerCase().includes(search.toLowerCase()) ||
                    user.username.toLowerCase().includes(search.toLowerCase()) ||
                    user.email.toLowerCase().includes(search.toLowerCase())
                );
            }
            
            if (role) {
                filteredUsers = filteredUsers.filter(user => user.role === role);
            }
            
            if (status) {
                filteredUsers = filteredUsers.filter(user => 
                    status === 'active' ? user.is_active : !user.is_active
                );
            }
            
            displayUsers(filteredUsers);
        } else {
            throw new Error('Failed to load users');
        }
    } catch (error) {
        console.error('Error loading users:', error);
        showToast('Error loading users', 'error');
    } finally {
        hideLoading();
    }
}

function displayUsers(users) {
    const tbody = document.getElementById('users-table-body');
    
    if (users.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="6" class="px-6 py-12 text-center text-gray-500">
                    <i class="fas fa-users text-4xl mb-4 text-gray-300"></i>
                    <p class="text-lg font-medium mb-2">No users found</p>
                    <p class="text-sm">Create your first user to get started</p>
                </td>
            </tr>
        `;
        return;
    }
    
    tbody.innerHTML = users.map(user => `
        <tr class="hover:bg-gray-50">
            <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                    <div class="w-10 h-10 bg-primary-600 rounded-full flex items-center justify-center text-white font-medium mr-3">
                        ${user.full_name.charAt(0).toUpperCase()}
                    </div>
                    <div>
                        <div class="text-sm font-medium text-gray-900">${user.full_name}</div>
                        <div class="text-sm text-gray-500">${user.email}</div>
                    </div>
                </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900 font-mono">${user.username}</div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getRoleBadgeClass(user.role)}">
                    ${user.role.charAt(0).toUpperCase() + user.role.slice(1)}
                </span>
                ${user.is_superuser ? '<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800 ml-1">Super</span>' : ''}
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${user.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}">
                    ${user.is_active ? 'Active' : 'Inactive'}
                </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                ${new Date(user.created_at).toLocaleDateString()}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <div class="flex space-x-2">
                    <button onclick="editUser(${user.id})" class="text-blue-600 hover:text-blue-800" title="Edit">
                        <i class="fas fa-edit"></i>
                    </button>
                    ${!user.is_superuser ? `
                        <button onclick="deleteUser(${user.id}, '${user.full_name}')" class="text-red-600 hover:text-red-800" title="Delete">
                            <i class="fas fa-trash"></i>
                        </button>
                    ` : ''}
                </div>
            </td>
        </tr>
    `).join('');
}

function getRoleBadgeClass(role) {
    switch (role) {
        case 'admin':
            return 'bg-red-100 text-red-800';
        case 'editor':
            return 'bg-blue-100 text-blue-800';
        case 'viewer':
            return 'bg-gray-100 text-gray-800';
        default:
            return 'bg-gray-100 text-gray-800';
    }
}

function openCreateUserModal() {
    currentEditingUserId = null;
    document.getElementById('user-modal-title').textContent = 'Create New User';
    document.getElementById('user-save-button-text').textContent = 'Create User';
    document.getElementById('password-required').textContent = '*';
    document.getElementById('password-help').textContent = 'Minimum 6 characters';
    document.getElementById('user-password').required = true;
    document.getElementById('user-form').reset();
    document.getElementById('user-active').checked = true;
    document.getElementById('user-modal').classList.remove('hidden');
}

function closeUserModal() {
    document.getElementById('user-modal').classList.add('hidden');
    currentEditingUserId = null;
}

async function editUser(userId) {
    try {
        const response = await fetch(`/api/cms/users/${userId}`, {
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('access_token')}`
            }
        });
        
        if (response.ok) {
            const user = await response.json();
            
            currentEditingUserId = userId;
            document.getElementById('user-modal-title').textContent = 'Edit User';
            document.getElementById('user-save-button-text').textContent = 'Update User';
            document.getElementById('password-required').textContent = '';
            document.getElementById('password-help').textContent = 'Leave blank to keep current password';
            document.getElementById('user-password').required = false;
            
            // Populate form
            document.getElementById('user-id').value = user.id;
            document.getElementById('user-full-name').value = user.full_name;
            document.getElementById('user-username').value = user.username;
            document.getElementById('user-email').value = user.email;
            document.getElementById('user-role').value = user.role;
            document.getElementById('user-active').checked = user.is_active;
            document.getElementById('user-password').value = '';
            
            document.getElementById('user-modal').classList.remove('hidden');
        } else {
            throw new Error('Failed to load user');
        }
    } catch (error) {
        console.error('Error loading user:', error);
        showToast('Error loading user', 'error');
    }
}

async function handleUserSubmit(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const userData = {
        full_name: formData.get('full_name'),
        username: formData.get('username'),
        email: formData.get('email'),
        role: formData.get('role'),
        is_active: formData.has('is_active')
    };
    
    // Only include password if it's provided
    const password = formData.get('password');
    if (password) {
        if (password.length < 6) {
            showToast('Password must be at least 6 characters long', 'error');
            return;
        }
        userData.password = password;
    } else if (!currentEditingUserId) {
        showToast('Password is required for new users', 'error');
        return;
    }
    
    try {
        showLoading();
        
        const url = currentEditingUserId 
            ? `/api/cms/users/${currentEditingUserId}`
            : '/api/cms/users';
        
        const method = currentEditingUserId ? 'PUT' : 'POST';
        
        const response = await fetch(url, {
            method: method,
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(userData)
        });
        
        if (response.ok) {
            showToast(currentEditingUserId ? 'User updated successfully' : 'User created successfully', 'success');
            closeUserModal();
            loadUsers();
        } else {
            const error = await response.json();
            throw new Error(error.detail || 'Failed to save user');
        }
    } catch (error) {
        console.error('Error saving user:', error);
        showToast(error.message, 'error');
    } finally {
        hideLoading();
    }
}

function deleteUser(userId, userName) {
    currentDeleteUserId = userId;
    document.getElementById('delete-user-name').textContent = userName;
    document.getElementById('delete-user-modal').classList.remove('hidden');
}

function closeDeleteUserModal() {
    document.getElementById('delete-user-modal').classList.add('hidden');
    currentDeleteUserId = null;
}

async function confirmDeleteUser() {
    if (!currentDeleteUserId) return;
    
    try {
        showLoading();
        
        const response = await fetch(`/api/cms/users/${currentDeleteUserId}`, {
            method: 'DELETE',
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('access_token')}`
            }
        });
        
        if (response.ok) {
            showToast('User deleted successfully', 'success');
            closeDeleteUserModal();
            loadUsers();
        } else {
            const error = await response.json();
            throw new Error(error.detail || 'Failed to delete user');
        }
    } catch (error) {
        console.error('Error deleting user:', error);
        showToast(error.message, 'error');
    } finally {
        hideLoading();
    }
}

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}
</script>
{% endblock %}
