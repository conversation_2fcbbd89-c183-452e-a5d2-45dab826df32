{% extends "admin/base.html" %}

{% block page_title %}Content Blocks{% endblock %}
{% block page_description %}Manage dynamic content blocks for all website sections{% endblock %}

{% block extra_head %}
<!-- Quill.js Rich Text Editor -->
<link href="https://cdn.quilljs.com/1.3.6/quill.snow.css" rel="stylesheet">
<script src="https://cdn.quilljs.com/1.3.6/quill.min.js"></script>
{% endblock %}

{% block content %}
<!-- Header Actions -->
<div class="flex justify-between items-center mb-6">
    <div>
        <h3 class="text-lg font-semibold text-gray-900">Content Blocks</h3>
        <p class="text-sm text-gray-600">Manage dynamic content blocks used throughout the website</p>
    </div>
    <button onclick="openCreateBlockModal()" class="bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors">
        <i class="fas fa-plus mr-2"></i>New Content Block
    </button>
</div>

<!-- Filters and Search -->
<div class="bg-white rounded-lg shadow mb-6 p-4">
    <div class="flex flex-col md:flex-row gap-4">
        <div class="flex-1">
            <input 
                type="text" 
                id="search-blocks" 
                placeholder="Search content blocks..." 
                class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
            >
        </div>
        <div class="flex gap-2">
            <select id="filter-type" class="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500">
                <option value="">All Types</option>
                <option value="text">Text</option>
                <option value="html">HTML</option>
                <option value="image">Image</option>
                <option value="contact">Contact Info</option>
                <option value="list">List</option>
                <option value="table">Table</option>
            </select>
            <select id="filter-section" class="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500">
                <option value="">All Sections</option>
                <option value="header">Header</option>
                <option value="footer">Footer</option>
                <option value="homepage">Homepage</option>
                <option value="about">About</option>
                <option value="admissions">Admissions</option>
                <option value="facilities">Facilities</option>
                <option value="contact">Contact</option>
            </select>
        </div>
    </div>
</div>

<!-- Content Blocks Table -->
<div class="bg-white rounded-lg shadow overflow-hidden">
    <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Block
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Type
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Section
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Last Updated
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                    </th>
                </tr>
            </thead>
            <tbody id="blocks-table-body" class="bg-white divide-y divide-gray-200">
                <!-- Content blocks will be loaded here -->
                <tr>
                    <td colspan="5" class="px-6 py-12 text-center text-gray-500">
                        <i class="fas fa-spinner fa-spin text-2xl mb-2"></i>
                        <p>Loading content blocks...</p>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
</div>

<!-- Create/Edit Content Block Modal -->
<div id="block-modal" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
    <div class="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <div class="p-6 border-b border-gray-200">
            <div class="flex justify-between items-center">
                <h3 id="block-modal-title" class="text-lg font-semibold text-gray-900">Create Content Block</h3>
                <button onclick="closeBlockModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
        </div>
        
        <form id="block-form" class="p-6 space-y-6">
            <input type="hidden" id="block-id" name="id">
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="block-key" class="block text-sm font-medium text-gray-700 mb-2">Block Key *</label>
                    <input 
                        type="text" 
                        id="block-key" 
                        name="key" 
                        required
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                        placeholder="e.g., institute_name, contact_phone"
                    >
                    <p class="text-xs text-gray-500 mt-1">Unique identifier for this content block</p>
                </div>
                
                <div>
                    <label for="block-title" class="block text-sm font-medium text-gray-700 mb-2">Title *</label>
                    <input 
                        type="text" 
                        id="block-title" 
                        name="title" 
                        required
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                        placeholder="Human-readable title"
                    >
                </div>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="block-type" class="block text-sm font-medium text-gray-700 mb-2">Content Type *</label>
                    <select 
                        id="block-type" 
                        name="type" 
                        required
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                        onchange="handleBlockTypeChange()"
                    >
                        <option value="">Select type</option>
                        <option value="text">Plain Text</option>
                        <option value="html">Rich HTML</option>
                        <option value="image">Image URL</option>
                        <option value="contact">Contact Information</option>
                        <option value="list">List Items</option>
                        <option value="table">Table Data</option>
                    </select>
                </div>
                
                <div>
                    <label for="block-section" class="block text-sm font-medium text-gray-700 mb-2">Section</label>
                    <select 
                        id="block-section" 
                        name="section"
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                    >
                        <option value="">Select section</option>
                        <option value="header">Header</option>
                        <option value="footer">Footer</option>
                        <option value="homepage">Homepage</option>
                        <option value="about">About</option>
                        <option value="admissions">Admissions</option>
                        <option value="facilities">Facilities</option>
                        <option value="contact">Contact</option>
                        <option value="global">Global</option>
                    </select>
                </div>
            </div>
            
            <div>
                <label for="block-description" class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                <textarea 
                    id="block-description" 
                    name="description" 
                    rows="2"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                    placeholder="Brief description of this content block"
                ></textarea>
            </div>
            
            <!-- Content Input Area - Changes based on type -->
            <div id="content-input-area">
                <!-- Text Input -->
                <div id="text-input" class="hidden">
                    <label for="block-text-content" class="block text-sm font-medium text-gray-700 mb-2">Text Content</label>
                    <textarea 
                        id="block-text-content" 
                        name="text_content" 
                        rows="4"
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                        placeholder="Enter plain text content"
                    ></textarea>
                </div>
                
                <!-- HTML Input -->
                <div id="html-input" class="hidden">
                    <label for="block-html-content" class="block text-sm font-medium text-gray-700 mb-2">HTML Content</label>
                    <div id="quill-editor" style="height: 300px;"></div>
                    <input type="hidden" id="block-html-content" name="html_content">
                </div>
                
                <!-- Image Input -->
                <div id="image-input" class="hidden">
                    <label for="block-image-url" class="block text-sm font-medium text-gray-700 mb-2">Image URL</label>
                    <input 
                        type="url" 
                        id="block-image-url" 
                        name="image_url"
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                        placeholder="https://example.com/image.jpg"
                    >
                    <div class="mt-2">
                        <button type="button" onclick="uploadImage()" class="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors">
                            <i class="fas fa-upload mr-2"></i>Upload Image
                        </button>
                    </div>
                </div>
                
                <!-- Contact Input -->
                <div id="contact-input" class="hidden">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="contact-phone" class="block text-sm font-medium text-gray-700 mb-2">Phone</label>
                            <input 
                                type="tel" 
                                id="contact-phone" 
                                name="contact_phone"
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                            >
                        </div>
                        <div>
                            <label for="contact-email" class="block text-sm font-medium text-gray-700 mb-2">Email</label>
                            <input 
                                type="email" 
                                id="contact-email" 
                                name="contact_email"
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                            >
                        </div>
                        <div class="md:col-span-2">
                            <label for="contact-address" class="block text-sm font-medium text-gray-700 mb-2">Address</label>
                            <textarea 
                                id="contact-address" 
                                name="contact_address" 
                                rows="3"
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                            ></textarea>
                        </div>
                    </div>
                </div>
                
                <!-- List Input -->
                <div id="list-input" class="hidden">
                    <label class="block text-sm font-medium text-gray-700 mb-2">List Items</label>
                    <div id="list-items-container">
                        <div class="flex items-center space-x-2 mb-2">
                            <input 
                                type="text" 
                                class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                                placeholder="List item"
                            >
                            <button type="button" onclick="removeListItem(this)" class="px-3 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                    <button type="button" onclick="addListItem()" class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                        <i class="fas fa-plus mr-2"></i>Add Item
                    </button>
                </div>
            </div>
            
            <div class="flex items-center space-x-6">
                <label class="flex items-center">
                    <input 
                        type="checkbox" 
                        id="block-active" 
                        name="is_active"
                        checked
                        class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                    >
                    <span class="ml-2 text-sm text-gray-700">Active</span>
                </label>
            </div>
            
            <div class="flex justify-end space-x-4 pt-6 border-t border-gray-200">
                <button type="button" onclick="closeBlockModal()" class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors">
                    Cancel
                </button>
                <button type="submit" class="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors">
                    <span id="block-save-button-text">Save Block</span>
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div id="delete-block-modal" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
    <div class="bg-white rounded-lg shadow-xl w-full max-w-md">
        <div class="p-6">
            <div class="flex items-center mb-4">
                <div class="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center mr-4">
                    <i class="fas fa-exclamation-triangle text-red-600"></i>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-900">Delete Content Block</h3>
                    <p class="text-sm text-gray-600">This action cannot be undone</p>
                </div>
            </div>
            
            <p class="text-gray-700 mb-6">Are you sure you want to delete "<span id="delete-block-title"></span>"?</p>
            
            <div class="flex justify-end space-x-4">
                <button onclick="closeDeleteBlockModal()" class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors">
                    Cancel
                </button>
                <button onclick="confirmDeleteBlock()" class="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors">
                    Delete Block
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
let currentEditingBlockId = null;
let currentDeleteBlockId = null;
let blockQuillEditor = null;

document.addEventListener('DOMContentLoaded', function() {
    loadContentBlocks();
    setupEventListeners();
});

function setupEventListeners() {
    // Search and filters
    document.getElementById('search-blocks').addEventListener('input', debounce(loadContentBlocks, 300));
    document.getElementById('filter-type').addEventListener('change', loadContentBlocks);
    document.getElementById('filter-section').addEventListener('change', loadContentBlocks);

    // Form submission
    document.getElementById('block-form').addEventListener('submit', handleBlockSubmit);

    // Auto-generate key from title
    document.getElementById('block-title').addEventListener('input', function() {
        const title = this.value;
        const key = title.toLowerCase()
            .replace(/[^a-z0-9\s]/g, '')
            .replace(/\s+/g, '_')
            .replace(/_+/g, '_')
            .trim('_');
        document.getElementById('block-key').value = key;
    });
}

function handleBlockTypeChange() {
    const type = document.getElementById('block-type').value;

    // Hide all input areas
    document.querySelectorAll('#content-input-area > div').forEach(div => {
        div.classList.add('hidden');
    });

    // Show relevant input area
    switch (type) {
        case 'text':
            document.getElementById('text-input').classList.remove('hidden');
            break;
        case 'html':
            document.getElementById('html-input').classList.remove('hidden');
            initBlockQuillEditor();
            break;
        case 'image':
            document.getElementById('image-input').classList.remove('hidden');
            break;
        case 'contact':
            document.getElementById('contact-input').classList.remove('hidden');
            break;
        case 'list':
            document.getElementById('list-input').classList.remove('hidden');
            break;
    }
}

function initBlockQuillEditor() {
    if (blockQuillEditor) {
        return; // Already initialized
    }

    const toolbarOptions = [
        ['bold', 'italic', 'underline'],
        [{ 'header': [1, 2, 3, false] }],
        [{ 'list': 'ordered'}, { 'list': 'bullet' }],
        ['link', 'image'],
        ['clean']
    ];

    blockQuillEditor = new Quill('#quill-editor', {
        modules: {
            toolbar: toolbarOptions
        },
        placeholder: 'Enter HTML content...',
        theme: 'snow'
    });

    // Update hidden input when editor content changes
    blockQuillEditor.on('text-change', function() {
        document.getElementById('block-html-content').value = blockQuillEditor.root.innerHTML;
    });
}

function destroyBlockQuillEditor() {
    if (blockQuillEditor) {
        document.getElementById('quill-editor').innerHTML = '';
        blockQuillEditor = null;
    }
}

async function loadContentBlocks() {
    showLoading();

    const search = document.getElementById('search-blocks').value;
    const type = document.getElementById('filter-type').value;
    const section = document.getElementById('filter-section').value;

    try {
        // Mock content blocks data - replace with actual API call
        const mockBlocks = [
            {
                id: 1,
                key: 'institute_name',
                title: 'Institute Name',
                type: 'text',
                section: 'global',
                content: 'S.N. Pvt. Industrial Training Institute',
                description: 'Official name of the institute',
                is_active: true,
                updated_at: '2024-01-15T10:00:00'
            },
            {
                id: 2,
                key: 'contact_phone',
                title: 'Contact Phone',
                type: 'text',
                section: 'contact',
                content: '01564-275628',
                description: 'Primary contact phone number',
                is_active: true,
                updated_at: '2024-01-14T15:30:00'
            },
            {
                id: 3,
                key: 'about_intro',
                title: 'About Introduction',
                type: 'html',
                section: 'about',
                content: '<p>S.N. Pvt. Industrial Training Institute was established in 2009...</p>',
                description: 'Introduction text for about page',
                is_active: true,
                updated_at: '2024-01-13T09:15:00'
            },
            {
                id: 4,
                key: 'hero_image',
                title: 'Homepage Hero Image',
                type: 'image',
                section: 'homepage',
                content: '/assets/images/hero-bg.jpg',
                description: 'Main hero image for homepage',
                is_active: true,
                updated_at: '2024-01-12T14:20:00'
            }
        ];

        // Filter blocks based on search and filters
        let filteredBlocks = mockBlocks;

        if (search) {
            filteredBlocks = filteredBlocks.filter(block =>
                block.title.toLowerCase().includes(search.toLowerCase()) ||
                block.key.toLowerCase().includes(search.toLowerCase()) ||
                block.description.toLowerCase().includes(search.toLowerCase())
            );
        }

        if (type) {
            filteredBlocks = filteredBlocks.filter(block => block.type === type);
        }

        if (section) {
            filteredBlocks = filteredBlocks.filter(block => block.section === section);
        }

        displayContentBlocks(filteredBlocks);

    } catch (error) {
        console.error('Error loading content blocks:', error);
        showToast('Error loading content blocks', 'error');
    } finally {
        hideLoading();
    }
}

function displayContentBlocks(blocks) {
    const tbody = document.getElementById('blocks-table-body');

    if (blocks.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="5" class="px-6 py-12 text-center text-gray-500">
                    <i class="fas fa-cube text-4xl mb-4 text-gray-300"></i>
                    <p class="text-lg font-medium mb-2">No content blocks found</p>
                    <p class="text-sm">Create your first content block to get started</p>
                </td>
            </tr>
        `;
        return;
    }

    tbody.innerHTML = blocks.map(block => `
        <tr class="hover:bg-gray-50">
            <td class="px-6 py-4">
                <div>
                    <div class="text-sm font-medium text-gray-900">${block.title}</div>
                    <div class="text-sm text-gray-500 mt-1">${block.key}</div>
                    <div class="text-xs text-gray-400 mt-1">${block.description || 'No description'}</div>
                    ${!block.is_active ? '<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 mt-2">Inactive</span>' : ''}
                </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getTypeBadgeClass(block.type)}">
                    ${block.type.charAt(0).toUpperCase() + block.type.slice(1)}
                </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                ${block.section ? block.section.charAt(0).toUpperCase() + block.section.slice(1) : 'Global'}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                ${new Date(block.updated_at).toLocaleDateString()}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <div class="flex space-x-2">
                    <button onclick="editBlock(${block.id})" class="text-blue-600 hover:text-blue-800" title="Edit">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button onclick="deleteBlock(${block.id}, '${block.title}')" class="text-red-600 hover:text-red-800" title="Delete">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
}

function getTypeBadgeClass(type) {
    switch (type) {
        case 'text':
            return 'bg-blue-100 text-blue-800';
        case 'html':
            return 'bg-green-100 text-green-800';
        case 'image':
            return 'bg-purple-100 text-purple-800';
        case 'contact':
            return 'bg-yellow-100 text-yellow-800';
        case 'list':
            return 'bg-indigo-100 text-indigo-800';
        default:
            return 'bg-gray-100 text-gray-800';
    }
}

function openCreateBlockModal() {
    currentEditingBlockId = null;
    document.getElementById('block-modal-title').textContent = 'Create Content Block';
    document.getElementById('block-save-button-text').textContent = 'Save Block';
    document.getElementById('block-form').reset();

    // Hide all input areas
    document.querySelectorAll('#content-input-area > div').forEach(div => {
        div.classList.add('hidden');
    });

    // Clear Quill editor
    destroyBlockQuillEditor();

    document.getElementById('block-modal').classList.remove('hidden');
}

function closeBlockModal() {
    document.getElementById('block-modal').classList.add('hidden');
    currentEditingBlockId = null;
    destroyBlockQuillEditor();
}

function addListItem() {
    const container = document.getElementById('list-items-container');
    const newItem = document.createElement('div');
    newItem.className = 'flex items-center space-x-2 mb-2';
    newItem.innerHTML = `
        <input
            type="text"
            class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
            placeholder="List item"
        >
        <button type="button" onclick="removeListItem(this)" class="px-3 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700">
            <i class="fas fa-trash"></i>
        </button>
    `;
    container.appendChild(newItem);
}

function removeListItem(button) {
    button.parentElement.remove();
}

async function handleBlockSubmit(e) {
    e.preventDefault();

    const formData = new FormData(e.target);
    const type = formData.get('type');

    let content = '';

    // Get content based on type
    switch (type) {
        case 'text':
            content = formData.get('text_content');
            break;
        case 'html':
            content = blockQuillEditor ? blockQuillEditor.root.innerHTML : formData.get('html_content');
            break;
        case 'image':
            content = formData.get('image_url');
            break;
        case 'contact':
            content = JSON.stringify({
                phone: formData.get('contact_phone'),
                email: formData.get('contact_email'),
                address: formData.get('contact_address')
            });
            break;
        case 'list':
            const listItems = [];
            document.querySelectorAll('#list-items-container input').forEach(input => {
                if (input.value.trim()) {
                    listItems.push(input.value.trim());
                }
            });
            content = JSON.stringify(listItems);
            break;
    }

    const blockData = {
        key: formData.get('key'),
        title: formData.get('title'),
        type: type,
        section: formData.get('section'),
        description: formData.get('description'),
        content: content,
        is_active: formData.has('is_active')
    };

    try {
        showLoading();

        // Mock API call - replace with actual API when implemented
        console.log('Block data to save:', blockData);

        showToast(currentEditingBlockId ? 'Content block updated successfully' : 'Content block created successfully', 'success');
        closeBlockModal();
        loadContentBlocks();

    } catch (error) {
        console.error('Error saving content block:', error);
        showToast(error.message, 'error');
    } finally {
        hideLoading();
    }
}

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}
</script>
{% endblock %}
