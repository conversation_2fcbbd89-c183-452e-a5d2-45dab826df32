{% extends "admin/base.html" %}

{% block page_title %}Faculty Management{% endblock %}
{% block page_description %}Manage faculty members and their information{% endblock %}

{% block content %}
<!-- Header Actions -->
<div class="flex justify-between items-center mb-6">
    <div>
        <h3 class="text-lg font-semibold text-gray-900">Faculty Members</h3>
        <p class="text-sm text-gray-600">Manage faculty profiles and information</p>
    </div>
    <button onclick="openCreateFacultyModal()" class="bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors">
        <i class="fas fa-user-plus mr-2"></i>Add Faculty
    </button>
</div>

<!-- Filters -->
<div class="bg-white rounded-lg shadow mb-6 p-4">
    <div class="flex flex-col md:flex-row gap-4">
        <div class="flex-1">
            <input 
                type="text" 
                id="search-faculty" 
                placeholder="Search faculty..." 
                class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
            >
        </div>
        <div class="flex gap-2">
            <select id="filter-status" class="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500">
                <option value="">All Status</option>
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
            </select>
        </div>
    </div>
</div>

<!-- Faculty Grid -->
<div id="faculty-grid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
    <!-- Faculty cards will be loaded here -->
    <div class="col-span-full flex justify-center py-12">
        <div class="text-center text-gray-500">
            <i class="fas fa-spinner fa-spin text-2xl mb-2"></i>
            <p>Loading faculty...</p>
        </div>
    </div>
</div>

<!-- Create/Edit Faculty Modal -->
<div id="faculty-modal" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
    <div class="bg-white rounded-lg shadow-xl w-full max-w-3xl max-h-[90vh] overflow-y-auto">
        <div class="p-6 border-b border-gray-200">
            <div class="flex justify-between items-center">
                <h3 id="faculty-modal-title" class="text-lg font-semibold text-gray-900">Add Faculty Member</h3>
                <button onclick="closeFacultyModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
        </div>
        
        <form id="faculty-form" class="p-6 space-y-6">
            <input type="hidden" id="faculty-id" name="id">
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="faculty-name" class="block text-sm font-medium text-gray-700 mb-2">Full Name *</label>
                    <input 
                        type="text" 
                        id="faculty-name" 
                        name="name" 
                        required
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                        placeholder="Enter full name"
                    >
                </div>
                
                <div>
                    <label for="faculty-designation" class="block text-sm font-medium text-gray-700 mb-2">Designation</label>
                    <input 
                        type="text" 
                        id="faculty-designation" 
                        name="designation"
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                        placeholder="e.g., Senior Instructor, Principal"
                    >
                </div>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="faculty-qualification" class="block text-sm font-medium text-gray-700 mb-2">Qualification</label>
                    <input 
                        type="text" 
                        id="faculty-qualification" 
                        name="qualification"
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                        placeholder="e.g., B.Tech, M.Tech, Diploma"
                    >
                </div>
                
                <div>
                    <label for="faculty-experience" class="block text-sm font-medium text-gray-700 mb-2">Experience</label>
                    <input 
                        type="text" 
                        id="faculty-experience" 
                        name="experience"
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                        placeholder="e.g., 5 years, 10+ years"
                    >
                </div>
            </div>
            
            <div>
                <label for="faculty-specialization" class="block text-sm font-medium text-gray-700 mb-2">Specialization</label>
                <input 
                    type="text" 
                    id="faculty-specialization" 
                    name="specialization"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                    placeholder="e.g., Electrical Engineering, Industrial Training"
                >
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="faculty-email" class="block text-sm font-medium text-gray-700 mb-2">Email</label>
                    <input 
                        type="email" 
                        id="faculty-email" 
                        name="email"
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                        placeholder="<EMAIL>"
                    >
                </div>
                
                <div>
                    <label for="faculty-phone" class="block text-sm font-medium text-gray-700 mb-2">Phone</label>
                    <input 
                        type="tel" 
                        id="faculty-phone" 
                        name="phone"
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                        placeholder="+91 9876543210"
                    >
                </div>
            </div>
            
            <div>
                <label for="faculty-bio" class="block text-sm font-medium text-gray-700 mb-2">Biography</label>
                <textarea 
                    id="faculty-bio" 
                    name="bio" 
                    rows="4"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                    placeholder="Brief biography and achievements..."
                ></textarea>
            </div>
            
            <div>
                <label for="faculty-photo-url" class="block text-sm font-medium text-gray-700 mb-2">Photo URL</label>
                <input 
                    type="url" 
                    id="faculty-photo-url" 
                    name="photo_url"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                    placeholder="https://example.com/photo.jpg"
                >
                <p class="text-xs text-gray-500 mt-1">URL to faculty member's photo</p>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="faculty-display-order" class="block text-sm font-medium text-gray-700 mb-2">Display Order</label>
                    <input 
                        type="number" 
                        id="faculty-display-order" 
                        name="display_order"
                        min="0"
                        value="0"
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                    >
                    <p class="text-xs text-gray-500 mt-1">Lower numbers appear first</p>
                </div>
                
                <div class="flex items-center pt-6">
                    <label class="flex items-center">
                        <input 
                            type="checkbox" 
                            id="faculty-active" 
                            name="is_active"
                            checked
                            class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                        >
                        <span class="ml-2 text-sm text-gray-700">Active Faculty Member</span>
                    </label>
                </div>
            </div>
            
            <div class="flex justify-end space-x-4 pt-6 border-t border-gray-200">
                <button type="button" onclick="closeFacultyModal()" class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors">
                    Cancel
                </button>
                <button type="submit" class="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors">
                    <span id="faculty-save-button-text">Add Faculty</span>
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div id="delete-faculty-modal" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
    <div class="bg-white rounded-lg shadow-xl w-full max-w-md">
        <div class="p-6">
            <div class="flex items-center mb-4">
                <div class="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center mr-4">
                    <i class="fas fa-exclamation-triangle text-red-600"></i>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-900">Delete Faculty Member</h3>
                    <p class="text-sm text-gray-600">This action cannot be undone</p>
                </div>
            </div>
            
            <p class="text-gray-700 mb-6">Are you sure you want to delete "<span id="delete-faculty-name"></span>"?</p>
            
            <div class="flex justify-end space-x-4">
                <button onclick="closeDeleteFacultyModal()" class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors">
                    Cancel
                </button>
                <button onclick="confirmDeleteFaculty()" class="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors">
                    Delete Faculty
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
let currentEditingFacultyId = null;
let currentDeleteFacultyId = null;

document.addEventListener('DOMContentLoaded', function() {
    loadFaculty();
    setupEventListeners();
});

function setupEventListeners() {
    // Search and filters
    document.getElementById('search-faculty').addEventListener('input', debounce(loadFaculty, 300));
    document.getElementById('filter-status').addEventListener('change', loadFaculty);
    
    // Form submission
    document.getElementById('faculty-form').addEventListener('submit', handleFacultySubmit);
}

async function loadFaculty() {
    showLoading();
    
    const search = document.getElementById('search-faculty').value;
    const status = document.getElementById('filter-status').value;
    
    try {
        const response = await fetch('/api/cms/faculty');
        
        if (response.ok) {
            const faculty = await response.json();
            
            // Filter faculty based on search and filters
            let filteredFaculty = faculty;
            
            if (search) {
                filteredFaculty = filteredFaculty.filter(member => 
                    member.name.toLowerCase().includes(search.toLowerCase()) ||
                    (member.designation && member.designation.toLowerCase().includes(search.toLowerCase())) ||
                    (member.specialization && member.specialization.toLowerCase().includes(search.toLowerCase()))
                );
            }
            
            if (status) {
                filteredFaculty = filteredFaculty.filter(member => 
                    status === 'active' ? member.is_active : !member.is_active
                );
            }
            
            displayFaculty(filteredFaculty);
        } else {
            throw new Error('Failed to load faculty');
        }
    } catch (error) {
        console.error('Error loading faculty:', error);
        showToast('Error loading faculty', 'error');
    } finally {
        hideLoading();
    }
}

function displayFaculty(faculty) {
    const grid = document.getElementById('faculty-grid');
    
    if (faculty.length === 0) {
        grid.innerHTML = `
            <div class="col-span-full flex flex-col items-center justify-center py-12">
                <i class="fas fa-chalkboard-teacher text-6xl text-gray-300 mb-4"></i>
                <p class="text-lg font-medium text-gray-500 mb-2">No faculty members found</p>
                <p class="text-sm text-gray-400">Add your first faculty member to get started</p>
            </div>
        `;
        return;
    }
    
    grid.innerHTML = faculty.map(member => `
        <div class="bg-white rounded-lg shadow hover:shadow-lg transition-shadow p-6">
            <div class="flex items-center mb-4">
                <div class="w-16 h-16 bg-primary-600 rounded-full flex items-center justify-center text-white text-xl font-bold mr-4">
                    ${member.photo_url ? 
                        `<img src="${member.photo_url}" alt="${member.name}" class="w-16 h-16 rounded-full object-cover">` :
                        member.name.charAt(0).toUpperCase()
                    }
                </div>
                <div class="flex-1">
                    <h3 class="text-lg font-semibold text-gray-900">${member.name}</h3>
                    ${member.designation ? `<p class="text-sm text-gray-600">${member.designation}</p>` : ''}
                    <div class="flex items-center mt-1">
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${member.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}">
                            ${member.is_active ? 'Active' : 'Inactive'}
                        </span>
                    </div>
                </div>
            </div>
            
            <div class="space-y-2 text-sm text-gray-600 mb-4">
                ${member.qualification ? `<p><i class="fas fa-graduation-cap mr-2"></i>${member.qualification}</p>` : ''}
                ${member.experience ? `<p><i class="fas fa-clock mr-2"></i>${member.experience}</p>` : ''}
                ${member.specialization ? `<p><i class="fas fa-star mr-2"></i>${member.specialization}</p>` : ''}
                ${member.email ? `<p><i class="fas fa-envelope mr-2"></i>${member.email}</p>` : ''}
                ${member.phone ? `<p><i class="fas fa-phone mr-2"></i>${member.phone}</p>` : ''}
            </div>
            
            ${member.bio ? `
                <div class="mb-4">
                    <p class="text-sm text-gray-700 line-clamp-3">${member.bio}</p>
                </div>
            ` : ''}
            
            <div class="flex justify-end space-x-2 pt-4 border-t border-gray-200">
                <button onclick="editFaculty(${member.id})" class="text-blue-600 hover:text-blue-800 p-2" title="Edit">
                    <i class="fas fa-edit"></i>
                </button>
                <button onclick="deleteFaculty(${member.id}, '${member.name}')" class="text-red-600 hover:text-red-800 p-2" title="Delete">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        </div>
    `).join('');
}

function openCreateFacultyModal() {
    currentEditingFacultyId = null;
    document.getElementById('faculty-modal-title').textContent = 'Add Faculty Member';
    document.getElementById('faculty-save-button-text').textContent = 'Add Faculty';
    document.getElementById('faculty-form').reset();
    document.getElementById('faculty-active').checked = true;
    document.getElementById('faculty-modal').classList.remove('hidden');
}

function closeFacultyModal() {
    document.getElementById('faculty-modal').classList.add('hidden');
    currentEditingFacultyId = null;
}

async function editFaculty(facultyId) {
    try {
        const response = await fetch(`/api/cms/faculty/${facultyId}`, {
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('access_token')}`
            }
        });
        
        if (response.ok) {
            const faculty = await response.json();
            
            currentEditingFacultyId = facultyId;
            document.getElementById('faculty-modal-title').textContent = 'Edit Faculty Member';
            document.getElementById('faculty-save-button-text').textContent = 'Update Faculty';
            
            // Populate form
            document.getElementById('faculty-id').value = faculty.id;
            document.getElementById('faculty-name').value = faculty.name;
            document.getElementById('faculty-designation').value = faculty.designation || '';
            document.getElementById('faculty-qualification').value = faculty.qualification || '';
            document.getElementById('faculty-experience').value = faculty.experience || '';
            document.getElementById('faculty-specialization').value = faculty.specialization || '';
            document.getElementById('faculty-email').value = faculty.email || '';
            document.getElementById('faculty-phone').value = faculty.phone || '';
            document.getElementById('faculty-bio').value = faculty.bio || '';
            document.getElementById('faculty-photo-url').value = faculty.photo_url || '';
            document.getElementById('faculty-display-order').value = faculty.display_order;
            document.getElementById('faculty-active').checked = faculty.is_active;
            
            document.getElementById('faculty-modal').classList.remove('hidden');
        } else {
            throw new Error('Failed to load faculty member');
        }
    } catch (error) {
        console.error('Error loading faculty member:', error);
        showToast('Error loading faculty member', 'error');
    }
}

async function handleFacultySubmit(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const facultyData = {
        name: formData.get('name'),
        designation: formData.get('designation'),
        qualification: formData.get('qualification'),
        experience: formData.get('experience'),
        specialization: formData.get('specialization'),
        email: formData.get('email'),
        phone: formData.get('phone'),
        bio: formData.get('bio'),
        photo_url: formData.get('photo_url'),
        display_order: parseInt(formData.get('display_order')) || 0,
        is_active: formData.has('is_active')
    };
    
    try {
        showLoading();
        
        const url = currentEditingFacultyId 
            ? `/api/cms/faculty/${currentEditingFacultyId}`
            : '/api/cms/faculty';
        
        const method = currentEditingFacultyId ? 'PUT' : 'POST';
        
        const response = await fetch(url, {
            method: method,
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(facultyData)
        });
        
        if (response.ok) {
            showToast(currentEditingFacultyId ? 'Faculty updated successfully' : 'Faculty added successfully', 'success');
            closeFacultyModal();
            loadFaculty();
        } else {
            const error = await response.json();
            throw new Error(error.detail || 'Failed to save faculty member');
        }
    } catch (error) {
        console.error('Error saving faculty member:', error);
        showToast(error.message, 'error');
    } finally {
        hideLoading();
    }
}

function deleteFaculty(facultyId, facultyName) {
    currentDeleteFacultyId = facultyId;
    document.getElementById('delete-faculty-name').textContent = facultyName;
    document.getElementById('delete-faculty-modal').classList.remove('hidden');
}

function closeDeleteFacultyModal() {
    document.getElementById('delete-faculty-modal').classList.add('hidden');
    currentDeleteFacultyId = null;
}

async function confirmDeleteFaculty() {
    if (!currentDeleteFacultyId) return;
    
    try {
        showLoading();
        
        const response = await fetch(`/api/cms/faculty/${currentDeleteFacultyId}`, {
            method: 'DELETE',
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('access_token')}`
            }
        });
        
        if (response.ok) {
            showToast('Faculty member deleted successfully', 'success');
            closeDeleteFacultyModal();
            loadFaculty();
        } else {
            throw new Error('Failed to delete faculty member');
        }
    } catch (error) {
        console.error('Error deleting faculty member:', error);
        showToast('Error deleting faculty member', 'error');
    } finally {
        hideLoading();
    }
}

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}
</script>
{% endblock %}
