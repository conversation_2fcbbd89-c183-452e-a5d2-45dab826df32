"""
CRUD operations for CMS models
"""
from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_
from . import models, schemas
from .auth import get_password_hash

# User CRUD
def get_user(db: Session, user_id: int) -> Optional[models.User]:
    return db.query(models.User).filter(models.User.id == user_id).first()

def get_user_by_username(db: Session, username: str) -> Optional[models.User]:
    return db.query(models.User).filter(models.User.username == username).first()

def get_user_by_email(db: Session, email: str) -> Optional[models.User]:
    return db.query(models.User).filter(models.User.email == email).first()

def get_users(db: Session, skip: int = 0, limit: int = 100) -> List[models.User]:
    return db.query(models.User).offset(skip).limit(limit).all()

def create_user(db: Session, user: schemas.UserCreate) -> models.User:
    hashed_password = get_password_hash(user.password)
    db_user = models.User(
        username=user.username,
        email=user.email,
        full_name=user.full_name,
        hashed_password=hashed_password,
        is_active=user.is_active,
        role=user.role
    )
    db.add(db_user)
    db.commit()
    db.refresh(db_user)
    return db_user

def update_user(db: Session, user_id: int, user_update: schemas.UserUpdate) -> Optional[models.User]:
    db_user = get_user(db, user_id)
    if not db_user:
        return None
    
    update_data = user_update.dict(exclude_unset=True)
    if "password" in update_data:
        update_data["hashed_password"] = get_password_hash(update_data.pop("password"))
    
    for field, value in update_data.items():
        setattr(db_user, field, value)
    
    db.commit()
    db.refresh(db_user)
    return db_user

def delete_user(db: Session, user_id: int) -> bool:
    db_user = get_user(db, user_id)
    if not db_user:
        return False
    db.delete(db_user)
    db.commit()
    return True

# Page CRUD
def get_page(db: Session, page_id: int) -> Optional[models.Page]:
    return db.query(models.Page).filter(models.Page.id == page_id).first()

def get_page_by_slug(db: Session, slug: str) -> Optional[models.Page]:
    return db.query(models.Page).filter(models.Page.slug == slug).first()

def get_pages(db: Session, skip: int = 0, limit: int = 100, published_only: bool = False) -> List[models.Page]:
    query = db.query(models.Page)
    if published_only:
        query = query.filter(models.Page.is_published == True)
    return query.offset(skip).limit(limit).all()

def create_page(db: Session, page: schemas.PageCreate, author_id: int) -> models.Page:
    db_page = models.Page(**page.dict(), author_id=author_id)
    db.add(db_page)
    db.commit()
    db.refresh(db_page)
    return db_page

def update_page(db: Session, page_id: int, page_update: schemas.PageUpdate) -> Optional[models.Page]:
    db_page = get_page(db, page_id)
    if not db_page:
        return None
    
    update_data = page_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_page, field, value)
    
    db.commit()
    db.refresh(db_page)
    return db_page

def delete_page(db: Session, page_id: int) -> bool:
    db_page = get_page(db, page_id)
    if not db_page:
        return False
    db.delete(db_page)
    db.commit()
    return True

# Media file CRUD
def get_media_file(db: Session, file_id: int) -> Optional[models.MediaFile]:
    return db.query(models.MediaFile).filter(models.MediaFile.id == file_id).first()

def get_media_files(db: Session, skip: int = 0, limit: int = 100) -> List[models.MediaFile]:
    return db.query(models.MediaFile).offset(skip).limit(limit).all()

def create_media_file(db: Session, media_file: schemas.MediaFileCreate, uploaded_by_id: int) -> models.MediaFile:
    db_media_file = models.MediaFile(**media_file.dict(), uploaded_by_id=uploaded_by_id)
    db.add(db_media_file)
    db.commit()
    db.refresh(db_media_file)
    return db_media_file

def update_media_file(db: Session, file_id: int, media_update: schemas.MediaFileUpdate) -> Optional[models.MediaFile]:
    db_media_file = get_media_file(db, file_id)
    if not db_media_file:
        return None
    
    update_data = media_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_media_file, field, value)
    
    db.commit()
    db.refresh(db_media_file)
    return db_media_file

def delete_media_file(db: Session, file_id: int) -> bool:
    db_media_file = get_media_file(db, file_id)
    if not db_media_file:
        return False
    db.delete(db_media_file)
    db.commit()
    return True

# Menu item CRUD
def get_menu_item(db: Session, item_id: int) -> Optional[models.MenuItem]:
    return db.query(models.MenuItem).filter(models.MenuItem.id == item_id).first()

def get_menu_items(db: Session, active_only: bool = False) -> List[models.MenuItem]:
    query = db.query(models.MenuItem).filter(models.MenuItem.parent_id.is_(None))
    if active_only:
        query = query.filter(models.MenuItem.is_active == True)
    return query.order_by(models.MenuItem.sort_order).all()

def create_menu_item(db: Session, menu_item: schemas.MenuItemCreate) -> models.MenuItem:
    db_menu_item = models.MenuItem(**menu_item.dict())
    db.add(db_menu_item)
    db.commit()
    db.refresh(db_menu_item)
    return db_menu_item

def update_menu_item(db: Session, item_id: int, menu_update: schemas.MenuItemUpdate) -> Optional[models.MenuItem]:
    db_menu_item = get_menu_item(db, item_id)
    if not db_menu_item:
        return None
    
    update_data = menu_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_menu_item, field, value)
    
    db.commit()
    db.refresh(db_menu_item)
    return db_menu_item

def delete_menu_item(db: Session, item_id: int) -> bool:
    db_menu_item = get_menu_item(db, item_id)
    if not db_menu_item:
        return False
    db.delete(db_menu_item)
    db.commit()
    return True

# Site settings CRUD
def get_site_setting(db: Session, key: str) -> Optional[models.SiteSettings]:
    return db.query(models.SiteSettings).filter(models.SiteSettings.key == key).first()

def get_site_settings(db: Session, public_only: bool = False) -> List[models.SiteSettings]:
    query = db.query(models.SiteSettings)
    if public_only:
        query = query.filter(models.SiteSettings.is_public == True)
    return query.all()

def create_or_update_setting(db: Session, setting: schemas.SiteSettingCreate) -> models.SiteSettings:
    db_setting = get_site_setting(db, setting.key)
    if db_setting:
        # Update existing
        for field, value in setting.dict().items():
            if field != "key":  # Don't update the key
                setattr(db_setting, field, value)
    else:
        # Create new
        db_setting = models.SiteSettings(**setting.dict())
        db.add(db_setting)
    
    db.commit()
    db.refresh(db_setting)
    return db_setting

def delete_site_setting(db: Session, key: str) -> bool:
    db_setting = get_site_setting(db, key)
    if not db_setting:
        return False
    db.delete(db_setting)
    db.commit()
    return True

# Faculty CRUD
def get_faculty(db: Session, faculty_id: int) -> Optional[models.Faculty]:
    return db.query(models.Faculty).filter(models.Faculty.id == faculty_id).first()

def get_faculty_list(db: Session, active_only: bool = False) -> List[models.Faculty]:
    query = db.query(models.Faculty)
    if active_only:
        query = query.filter(models.Faculty.is_active == True)
    return query.order_by(models.Faculty.display_order, models.Faculty.name).all()

def create_faculty(db: Session, faculty: schemas.FacultyCreate) -> models.Faculty:
    db_faculty = models.Faculty(**faculty.dict())
    db.add(db_faculty)
    db.commit()
    db.refresh(db_faculty)
    return db_faculty

def update_faculty(db: Session, faculty_id: int, faculty_update: schemas.FacultyUpdate) -> Optional[models.Faculty]:
    db_faculty = get_faculty(db, faculty_id)
    if not db_faculty:
        return None
    
    update_data = faculty_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_faculty, field, value)
    
    db.commit()
    db.refresh(db_faculty)
    return db_faculty

def delete_faculty(db: Session, faculty_id: int) -> bool:
    db_faculty = get_faculty(db, faculty_id)
    if not db_faculty:
        return False
    db.delete(db_faculty)
    db.commit()
    return True

# News CRUD
def get_news(db: Session, news_id: int) -> Optional[models.News]:
    return db.query(models.News).filter(models.News.id == news_id).first()

def get_news_by_slug(db: Session, slug: str) -> Optional[models.News]:
    return db.query(models.News).filter(models.News.slug == slug).first()

def get_news_list(db: Session, skip: int = 0, limit: int = 100, published_only: bool = False) -> List[models.News]:
    query = db.query(models.News)
    if published_only:
        query = query.filter(models.News.is_published == True)
    return query.order_by(models.News.publish_date.desc()).offset(skip).limit(limit).all()

def create_news(db: Session, news: schemas.NewsCreate, author_id: int) -> models.News:
    db_news = models.News(**news.dict(), author_id=author_id)
    db.add(db_news)
    db.commit()
    db.refresh(db_news)
    return db_news

def update_news(db: Session, news_id: int, news_update: schemas.NewsUpdate) -> Optional[models.News]:
    db_news = get_news(db, news_id)
    if not db_news:
        return None
    
    update_data = news_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_news, field, value)
    
    db.commit()
    db.refresh(db_news)
    return db_news

def delete_news(db: Session, news_id: int) -> bool:
    db_news = get_news(db, news_id)
    if not db_news:
        return False
    db.delete(db_news)
    db.commit()
    return True

# Contact form CRUD
def get_contact_form(db: Session, form_id: int) -> Optional[models.ContactForm]:
    return db.query(models.ContactForm).filter(models.ContactForm.id == form_id).first()

def get_contact_forms(db: Session, skip: int = 0, limit: int = 100, unread_only: bool = False) -> List[models.ContactForm]:
    query = db.query(models.ContactForm)
    if unread_only:
        query = query.filter(models.ContactForm.is_read == False)
    return query.order_by(models.ContactForm.created_at.desc()).offset(skip).limit(limit).all()

def create_contact_form(db: Session, contact_form: schemas.ContactFormCreate, ip_address: str = None, user_agent: str = None) -> models.ContactForm:
    db_contact_form = models.ContactForm(
        **contact_form.dict(),
        ip_address=ip_address,
        user_agent=user_agent
    )
    db.add(db_contact_form)
    db.commit()
    db.refresh(db_contact_form)
    return db_contact_form

def mark_contact_form_read(db: Session, form_id: int) -> Optional[models.ContactForm]:
    db_contact_form = get_contact_form(db, form_id)
    if not db_contact_form:
        return None
    
    db_contact_form.is_read = True
    db.commit()
    db.refresh(db_contact_form)
    return db_contact_form
