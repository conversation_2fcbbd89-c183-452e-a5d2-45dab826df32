"""
Pydantic schemas for API requests and responses
"""
from datetime import datetime
from typing import Optional, List
from pydantic import BaseModel, EmailStr

# User schemas
class UserBase(BaseModel):
    username: str
    email: EmailStr
    full_name: str
    is_active: bool = True
    role: str = "editor"

class UserCreate(UserBase):
    password: str

class UserUpdate(BaseModel):
    username: Optional[str] = None
    email: Optional[EmailStr] = None
    full_name: Optional[str] = None
    is_active: Optional[bool] = None
    role: Optional[str] = None
    password: Optional[str] = None

class User(UserBase):
    id: int
    is_superuser: bool
    created_at: datetime
    updated_at: Optional[datetime]
    
    class Config:
        from_attributes = True

# Authentication schemas
class Token(BaseModel):
    access_token: str
    token_type: str

class TokenData(BaseModel):
    username: Optional[str] = None

class LoginRequest(BaseModel):
    username: str
    password: str

# Page schemas
class PageBase(BaseModel):
    title: str
    slug: str
    content: Optional[str] = None
    meta_description: Optional[str] = None
    meta_keywords: Optional[str] = None
    is_published: bool = False
    is_homepage: bool = False
    template: str = "generic"
    parent_id: Optional[int] = None
    sort_order: int = 0

class PageCreate(PageBase):
    pass

class PageUpdate(BaseModel):
    title: Optional[str] = None
    slug: Optional[str] = None
    content: Optional[str] = None
    meta_description: Optional[str] = None
    meta_keywords: Optional[str] = None
    is_published: Optional[bool] = None
    is_homepage: Optional[bool] = None
    template: Optional[str] = None
    parent_id: Optional[int] = None
    sort_order: Optional[int] = None

class Page(PageBase):
    id: int
    author_id: int
    created_at: datetime
    updated_at: Optional[datetime]
    author: User
    
    class Config:
        from_attributes = True

# Media file schemas
class MediaFileBase(BaseModel):
    filename: str
    original_filename: str
    file_path: str
    file_size: Optional[int] = None
    mime_type: Optional[str] = None
    alt_text: Optional[str] = None
    caption: Optional[str] = None

class MediaFileCreate(MediaFileBase):
    pass

class MediaFileUpdate(BaseModel):
    alt_text: Optional[str] = None
    caption: Optional[str] = None

class MediaFile(MediaFileBase):
    id: int
    uploaded_by_id: int
    created_at: datetime
    uploaded_by: User
    
    class Config:
        from_attributes = True

# Menu item schemas
class MenuItemBase(BaseModel):
    title: str
    url: Optional[str] = None
    page_id: Optional[int] = None
    parent_id: Optional[int] = None
    sort_order: int = 0
    is_active: bool = True
    target: str = "_self"
    css_class: Optional[str] = None

class MenuItemCreate(MenuItemBase):
    pass

class MenuItemUpdate(BaseModel):
    title: Optional[str] = None
    url: Optional[str] = None
    page_id: Optional[int] = None
    parent_id: Optional[int] = None
    sort_order: Optional[int] = None
    is_active: Optional[bool] = None
    target: Optional[str] = None
    css_class: Optional[str] = None

class MenuItem(MenuItemBase):
    id: int
    created_at: datetime
    updated_at: Optional[datetime]
    children: List['MenuItem'] = []
    
    class Config:
        from_attributes = True

# Site settings schemas
class SiteSettingBase(BaseModel):
    key: str
    value: Optional[str] = None
    data_type: str = "string"
    description: Optional[str] = None
    is_public: bool = False

class SiteSettingCreate(SiteSettingBase):
    pass

class SiteSettingUpdate(BaseModel):
    value: Optional[str] = None
    description: Optional[str] = None
    is_public: Optional[bool] = None

class SiteSetting(SiteSettingBase):
    id: int
    created_at: datetime
    updated_at: Optional[datetime]
    
    class Config:
        from_attributes = True

# Faculty schemas
class FacultyBase(BaseModel):
    name: str
    designation: Optional[str] = None
    qualification: Optional[str] = None
    experience: Optional[str] = None
    specialization: Optional[str] = None
    email: Optional[EmailStr] = None
    phone: Optional[str] = None
    bio: Optional[str] = None
    photo_url: Optional[str] = None
    is_active: bool = True
    display_order: int = 0

class FacultyCreate(FacultyBase):
    pass

class FacultyUpdate(BaseModel):
    name: Optional[str] = None
    designation: Optional[str] = None
    qualification: Optional[str] = None
    experience: Optional[str] = None
    specialization: Optional[str] = None
    email: Optional[EmailStr] = None
    phone: Optional[str] = None
    bio: Optional[str] = None
    photo_url: Optional[str] = None
    is_active: Optional[bool] = None
    display_order: Optional[int] = None

class Faculty(FacultyBase):
    id: int
    created_at: datetime
    updated_at: Optional[datetime]
    
    class Config:
        from_attributes = True

# News schemas
class NewsBase(BaseModel):
    title: str
    slug: str
    excerpt: Optional[str] = None
    content: Optional[str] = None
    featured_image: Optional[str] = None
    is_published: bool = False
    is_featured: bool = False
    publish_date: Optional[datetime] = None

class NewsCreate(NewsBase):
    pass

class NewsUpdate(BaseModel):
    title: Optional[str] = None
    slug: Optional[str] = None
    excerpt: Optional[str] = None
    content: Optional[str] = None
    featured_image: Optional[str] = None
    is_published: Optional[bool] = None
    is_featured: Optional[bool] = None
    publish_date: Optional[datetime] = None

class News(NewsBase):
    id: int
    author_id: int
    created_at: datetime
    updated_at: Optional[datetime]
    author: User
    
    class Config:
        from_attributes = True

# Contact form schemas
class ContactFormBase(BaseModel):
    name: str
    email: EmailStr
    phone: Optional[str] = None
    subject: Optional[str] = None
    message: str

class ContactFormCreate(ContactFormBase):
    pass

class ContactForm(ContactFormBase):
    id: int
    is_read: bool
    ip_address: Optional[str]
    created_at: datetime
    
    class Config:
        from_attributes = True

# Response schemas
class MessageResponse(BaseModel):
    message: str

class PaginatedResponse(BaseModel):
    items: List
    total: int
    page: int
    per_page: int
    pages: int
