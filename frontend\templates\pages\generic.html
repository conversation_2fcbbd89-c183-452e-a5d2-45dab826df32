{% extends "base.html" %}

{% block content %}
<!-- Page Header -->
<section class="bg-gradient-to-r from-primary-700 to-primary-500 text-white py-16">
    <div class="container mx-auto px-4">
        <div class="text-center">
            <h1 class="text-4xl lg:text-5xl font-bold mb-4">
                {{ title.replace(' - SNPITC', '') }}
            </h1>
            <nav class="text-primary-200">
                <a href="/" class="hover:text-white transition-colors">Home</a>
                <span class="mx-2">/</span>
                <span>{{ title.replace(' - SNPITC', '') }}</span>
            </nav>
        </div>
    </div>
</section>

<!-- Page Content -->
<section class="py-16 bg-white">
    <div class="container mx-auto px-4">
        {% if page_content %}
        <div class="max-w-4xl mx-auto">
            <!-- Check if page_content is a string (new format) or object (old format) -->
            {% if page_content is string %}
            <!-- New simple HTML content format -->
            <div class="prose prose-lg max-w-none">
                {{ page_content | safe }}
            </div>
            {% else %}
            <!-- Old complex object format -->
            <!-- Page Title -->
            {% if page_content.metadata.title %}
            <h2 class="text-3xl font-bold text-gray-800 mb-8">{{ page_content.metadata.title }}</h2>
            {% endif %}

            <!-- Main Content -->
            <div class="prose prose-lg max-w-none">
                {% if page_content.content.text_content %}
                <div class="text-gray-700 leading-relaxed mb-8">
                    {{ page_content.content.text_content[:1000] }}...
                </div>
                {% endif %}
                
                <!-- Tables -->
                {% if page_content.content.tables %}
                <div class="mb-8">
                    {% for table in page_content.content.tables %}
                    {% if table.headers or table.rows %}
                    <div class="overflow-x-auto mb-6">
                        <table class="min-w-full bg-white border border-gray-200 rounded-lg">
                            {% if table.headers %}
                            <thead class="bg-gray-50">
                                <tr>
                                    {% for header in table.headers %}
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">
                                        {{ header }}
                                    </th>
                                    {% endfor %}
                                </tr>
                            </thead>
                            {% endif %}
                            <tbody class="divide-y divide-gray-200">
                                {% for row in table.rows %}
                                <tr class="hover:bg-gray-50">
                                    {% for cell in row %}
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 border-b">
                                        {{ cell }}
                                    </td>
                                    {% endfor %}
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% endif %}
                    {% endfor %}
                </div>
                {% endif %}
                
                <!-- Lists -->
                {% if page_content.content.lists %}
                <div class="mb-8">
                    {% for list_item in page_content.content.lists %}
                    {% if list_item.items %}
                    <div class="mb-6">
                        {% if list_item.type == 'ol' %}
                        <ol class="list-decimal list-inside space-y-2 text-gray-700">
                            {% for item in list_item.items %}
                            <li>{{ item }}</li>
                            {% endfor %}
                        </ol>
                        {% else %}
                        <ul class="list-disc list-inside space-y-2 text-gray-700">
                            {% for item in list_item.items %}
                            <li>{{ item }}</li>
                            {% endfor %}
                        </ul>
                        {% endif %}
                    </div>
                    {% endif %}
                    {% endfor %}
                </div>
                {% endif %}
                
                <!-- Forms -->
                {% if page_content.content.forms %}
                <div class="mb-8">
                    {% for form in page_content.content.forms %}
                    <div class="bg-gray-50 p-6 rounded-lg mb-6">
                        <h3 class="text-xl font-semibold text-gray-800 mb-4">Form</h3>
                        <form class="space-y-4">
                            {% for field in form.fields %}
                            <div>
                                {% if field.type == 'textarea' %}
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    {{ field.name or field.placeholder }}
                                </label>
                                <textarea 
                                    name="{{ field.name }}" 
                                    placeholder="{{ field.placeholder }}"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                                    rows="4"
                                    {% if field.required %}required{% endif %}
                                ></textarea>
                                {% else %}
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    {{ field.name or field.placeholder }}
                                </label>
                                <input 
                                    type="{{ field.type or 'text' }}" 
                                    name="{{ field.name }}"
                                    placeholder="{{ field.placeholder }}"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                                    {% if field.required %}required{% endif %}
                                />
                                {% endif %}
                            </div>
                            {% endfor %}
                            <button type="submit" class="bg-primary-600 text-white px-6 py-2 rounded-md hover:bg-primary-700 transition-colors">
                                Submit
                            </button>
                        </form>
                    </div>
                    {% endfor %}
                </div>
                {% endif %}
            </div>

            <!-- Download Links -->
            {% if page_content.media_urls.documents %}
            <div class="mt-12 bg-gray-50 p-6 rounded-lg">
                <h3 class="text-xl font-semibold text-gray-800 mb-4">
                    <i class="fas fa-download mr-2"></i>Downloads
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {% for doc_url in page_content.media_urls.documents %}
                    <a href="{{ doc_url }}" target="_blank" class="flex items-center p-3 bg-white rounded border hover:bg-gray-50 transition-colors">
                        <i class="fas fa-file-pdf text-red-500 mr-3"></i>
                        <span class="text-sm text-gray-700">{{ doc_url.split('/')[-1] }}</span>
                    </a>
                    {% endfor %}
                </div>
            </div>
            {% endif %}
            {% endif %}
        </div>
        {% else %}
        <div class="text-center py-16">
            <i class="fas fa-exclamation-triangle text-6xl text-gray-400 mb-4"></i>
            <h2 class="text-2xl font-semibold text-gray-600 mb-2">Content Not Available</h2>
            <p class="text-gray-500">The requested page content is currently not available.</p>
            <a href="/" class="inline-block mt-4 bg-primary-600 text-white px-6 py-2 rounded-lg hover:bg-primary-700 transition-colors">
                Return to Home
            </a>
        </div>
        {% endif %}
    </div>
</section>

<!-- Related Links -->
<section class="py-12 bg-gray-50">
    <div class="container mx-auto px-4">
        <div class="text-center">
            <h3 class="text-2xl font-semibold text-gray-800 mb-8">Related Information</h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <a href="/admission-criteria" class="bg-white p-6 rounded-lg shadow hover:shadow-lg transition-shadow">
                    <i class="fas fa-user-graduate text-3xl text-primary-600 mb-4"></i>
                    <h4 class="font-semibold text-gray-800 mb-2">Admission Process</h4>
                    <p class="text-sm text-gray-600">Learn about our admission criteria and process</p>
                </a>
                <a href="/trades" class="bg-white p-6 rounded-lg shadow hover:shadow-lg transition-shadow">
                    <i class="fas fa-tools text-3xl text-green-600 mb-4"></i>
                    <h4 class="font-semibold text-gray-800 mb-2">Available Courses</h4>
                    <p class="text-sm text-gray-600">Explore our NCVT and SCVT affiliated trades</p>
                </a>
                <a href="/contact" class="bg-white p-6 rounded-lg shadow hover:shadow-lg transition-shadow">
                    <i class="fas fa-phone text-3xl text-yellow-600 mb-4"></i>
                    <h4 class="font-semibold text-gray-800 mb-2">Contact Us</h4>
                    <p class="text-sm text-gray-600">Get in touch for more information</p>
                </a>
            </div>
        </div>
    </div>
</section>
{% endblock %}
