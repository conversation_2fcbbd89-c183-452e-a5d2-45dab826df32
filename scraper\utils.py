"""
Utility functions for web scraping
"""
import os
import re
import time
import hashlib
import requests
from pathlib import Path
from urllib.parse import urljoin, urlparse
from typing import Optional, Set, Dict, Any
from bs4 import BeautifulSoup

from .config import (
    DEFAULT_HEADERS, REQUEST_DELAY, MAX_RETRIES, TIMEOUT,
    IMAGE_EXTENSIONS, DOCUMENT_EXTENSIONS, IMAGES_DIR, DOCUMENTS_DIR
)


def clean_filename(filename: str) -> str:
    """Clean filename to be filesystem-safe"""
    # Remove or replace invalid characters
    filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
    # Remove multiple underscores
    filename = re.sub(r'_+', '_', filename)
    # Remove leading/trailing underscores and dots
    filename = filename.strip('_.')
    return filename


def get_file_hash(content: bytes) -> str:
    """Generate MD5 hash of file content"""
    return hashlib.md5(content).hexdigest()


def make_request(url: str, headers: Optional[Dict[str, str]] = None) -> Optional[requests.Response]:
    """Make HTTP request with retries and error handling"""
    if headers is None:
        headers = DEFAULT_HEADERS.copy()
    
    for attempt in range(MAX_RETRIES):
        try:
            time.sleep(REQUEST_DELAY)
            response = requests.get(url, headers=headers, timeout=TIMEOUT)
            response.raise_for_status()
            return response
        except requests.RequestException as e:
            print(f"Attempt {attempt + 1} failed for {url}: {e}")
            if attempt == MAX_RETRIES - 1:
                print(f"Failed to fetch {url} after {MAX_RETRIES} attempts")
                return None
    return None


def download_file(url: str, save_path: Path, base_url: str) -> Optional[str]:
    """Download file and save to specified path"""
    # Make URL absolute
    absolute_url = urljoin(base_url, url)
    
    response = make_request(absolute_url)
    if not response:
        return None
    
    try:
        # Create directory if it doesn't exist
        save_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Save file
        with open(save_path, 'wb') as f:
            f.write(response.content)
        
        print(f"Downloaded: {absolute_url} -> {save_path}")
        return str(save_path)
    except Exception as e:
        print(f"Error saving file {save_path}: {e}")
        return None


def extract_links(soup: BeautifulSoup, base_url: str) -> Set[str]:
    """Extract all links from a BeautifulSoup object"""
    links = set()
    
    # Extract href links
    for link in soup.find_all('a', href=True):
        href = link['href']
        if href and not href.startswith('#'):
            absolute_url = urljoin(base_url, href)
            links.add(absolute_url)
    
    return links


def extract_media_urls(soup: BeautifulSoup, base_url: str) -> Dict[str, Set[str]]:
    """Extract all media URLs (images, documents) from a BeautifulSoup object"""
    media_urls = {
        'images': set(),
        'documents': set()
    }
    
    # Extract image URLs
    for img in soup.find_all('img', src=True):
        src = img['src']
        if src:
            absolute_url = urljoin(base_url, src)
            parsed = urlparse(absolute_url)
            ext = Path(parsed.path).suffix.lower()
            if ext in IMAGE_EXTENSIONS:
                media_urls['images'].add(absolute_url)
    
    # Extract document URLs from links
    for link in soup.find_all('a', href=True):
        href = link['href']
        if href:
            absolute_url = urljoin(base_url, href)
            parsed = urlparse(absolute_url)
            ext = Path(parsed.path).suffix.lower()
            if ext in DOCUMENT_EXTENSIONS:
                media_urls['documents'].add(absolute_url)
    
    return media_urls


def clean_text(text: str) -> str:
    """Clean and normalize text content"""
    if not text:
        return ""
    
    # Remove extra whitespace
    text = re.sub(r'\s+', ' ', text)
    # Remove leading/trailing whitespace
    text = text.strip()
    return text


def extract_page_metadata(soup: BeautifulSoup) -> Dict[str, Any]:
    """Extract metadata from page"""
    metadata = {}
    
    # Title
    title_tag = soup.find('title')
    metadata['title'] = clean_text(title_tag.get_text()) if title_tag else ""
    
    # Meta tags
    meta_tags = {}
    for meta in soup.find_all('meta'):
        name = meta.get('name') or meta.get('property')
        content = meta.get('content')
        if name and content:
            meta_tags[name] = content
    metadata['meta_tags'] = meta_tags
    
    # Headings
    headings = {}
    for i in range(1, 7):
        h_tags = soup.find_all(f'h{i}')
        if h_tags:
            headings[f'h{i}'] = [clean_text(h.get_text()) for h in h_tags]
    metadata['headings'] = headings
    
    return metadata
