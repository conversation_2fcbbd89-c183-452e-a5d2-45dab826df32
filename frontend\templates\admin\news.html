{% extends "admin/base.html" %}

{% block page_title %}News Management{% endblock %}
{% block page_description %}Manage news articles and announcements{% endblock %}

{% block extra_head %}
<!-- Quill.js Rich Text Editor -->
<link href="https://cdn.quilljs.com/1.3.6/quill.snow.css" rel="stylesheet">
<script src="https://cdn.quilljs.com/1.3.6/quill.min.js"></script>
{% endblock %}

{% block content %}
<!-- Header Actions -->
<div class="flex justify-between items-center mb-6">
    <div>
        <h3 class="text-lg font-semibold text-gray-900">News & Announcements</h3>
        <p class="text-sm text-gray-600">Create and manage news articles and announcements</p>
    </div>
    <button onclick="openCreateNewsModal()" class="bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors">
        <i class="fas fa-plus mr-2"></i>New Article
    </button>
</div>

<!-- Filters and Search -->
<div class="bg-white rounded-lg shadow mb-6 p-4">
    <div class="flex flex-col md:flex-row gap-4">
        <div class="flex-1">
            <input 
                type="text" 
                id="search-news" 
                placeholder="Search news articles..." 
                class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
            >
        </div>
        <div class="flex gap-2">
            <select id="filter-status" class="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500">
                <option value="">All Status</option>
                <option value="published">Published</option>
                <option value="draft">Draft</option>
            </select>
            <select id="filter-featured" class="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500">
                <option value="">All Articles</option>
                <option value="featured">Featured</option>
                <option value="regular">Regular</option>
            </select>
        </div>
    </div>
</div>

<!-- News Table -->
<div class="bg-white rounded-lg shadow overflow-hidden">
    <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Article
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Author
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Published
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                    </th>
                </tr>
            </thead>
            <tbody id="news-table-body" class="bg-white divide-y divide-gray-200">
                <!-- News will be loaded here -->
                <tr>
                    <td colspan="5" class="px-6 py-12 text-center text-gray-500">
                        <i class="fas fa-spinner fa-spin text-2xl mb-2"></i>
                        <p>Loading news articles...</p>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
</div>

<!-- Create/Edit News Modal -->
<div id="news-modal" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
    <div class="bg-white rounded-lg shadow-xl w-full max-w-5xl max-h-[90vh] overflow-y-auto">
        <div class="p-6 border-b border-gray-200">
            <div class="flex justify-between items-center">
                <h3 id="news-modal-title" class="text-lg font-semibold text-gray-900">Create News Article</h3>
                <button onclick="closeNewsModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
        </div>
        
        <form id="news-form" class="p-6 space-y-6">
            <input type="hidden" id="news-id" name="id">
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="news-title" class="block text-sm font-medium text-gray-700 mb-2">Title *</label>
                    <input 
                        type="text" 
                        id="news-title" 
                        name="title" 
                        required
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                        placeholder="Enter article title"
                    >
                </div>
                
                <div>
                    <label for="news-slug" class="block text-sm font-medium text-gray-700 mb-2">Slug *</label>
                    <input 
                        type="text" 
                        id="news-slug" 
                        name="slug" 
                        required
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                        placeholder="article-url-slug"
                    >
                    <p class="text-xs text-gray-500 mt-1">URL-friendly version of the title</p>
                </div>
            </div>
            
            <div>
                <label for="news-excerpt" class="block text-sm font-medium text-gray-700 mb-2">Excerpt</label>
                <textarea 
                    id="news-excerpt" 
                    name="excerpt" 
                    rows="3"
                    maxlength="300"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                    placeholder="Brief summary of the article (max 300 characters)"
                ></textarea>
                <p class="text-xs text-gray-500 mt-1"><span id="excerpt-count">0</span>/300 characters</p>
            </div>
            
            <div>
                <label for="news-content" class="block text-sm font-medium text-gray-700 mb-2">Content</label>
                <textarea 
                    id="news-content" 
                    name="content" 
                    rows="15"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                    placeholder="Enter article content..."
                ></textarea>
                <p class="text-xs text-gray-500 mt-1">Use the rich text editor to format your content with headings, lists, images, and more.</p>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="news-featured-image" class="block text-sm font-medium text-gray-700 mb-2">Featured Image URL</label>
                    <input 
                        type="url" 
                        id="news-featured-image" 
                        name="featured_image"
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                        placeholder="https://example.com/image.jpg"
                    >
                    <p class="text-xs text-gray-500 mt-1">URL to the featured image for this article</p>
                </div>
                
                <div>
                    <label for="news-publish-date" class="block text-sm font-medium text-gray-700 mb-2">Publish Date</label>
                    <input 
                        type="datetime-local" 
                        id="news-publish-date" 
                        name="publish_date"
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                    >
                    <p class="text-xs text-gray-500 mt-1">Leave empty to use current date/time</p>
                </div>
            </div>
            
            <div class="flex items-center space-x-6">
                <label class="flex items-center">
                    <input 
                        type="checkbox" 
                        id="news-published" 
                        name="is_published"
                        class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                    >
                    <span class="ml-2 text-sm text-gray-700">Published</span>
                </label>
                
                <label class="flex items-center">
                    <input 
                        type="checkbox" 
                        id="news-featured" 
                        name="is_featured"
                        class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                    >
                    <span class="ml-2 text-sm text-gray-700">Featured Article</span>
                </label>
            </div>
            
            <div class="flex justify-end space-x-4 pt-6 border-t border-gray-200">
                <button type="button" onclick="closeNewsModal()" class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors">
                    Cancel
                </button>
                <button type="submit" class="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors">
                    <span id="news-save-button-text">Save Article</span>
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div id="delete-news-modal" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
    <div class="bg-white rounded-lg shadow-xl w-full max-w-md">
        <div class="p-6">
            <div class="flex items-center mb-4">
                <div class="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center mr-4">
                    <i class="fas fa-exclamation-triangle text-red-600"></i>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-900">Delete Article</h3>
                    <p class="text-sm text-gray-600">This action cannot be undone</p>
                </div>
            </div>
            
            <p class="text-gray-700 mb-6">Are you sure you want to delete "<span id="delete-news-title"></span>"?</p>
            
            <div class="flex justify-end space-x-4">
                <button onclick="closeDeleteNewsModal()" class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors">
                    Cancel
                </button>
                <button onclick="confirmDeleteNews()" class="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors">
                    Delete Article
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
let currentEditingNewsId = null;
let currentDeleteNewsId = null;
let newsTinyMCEInstance = null;

document.addEventListener('DOMContentLoaded', function() {
    loadNews();
    setupEventListeners();
    initNewsTinyMCE();
});

function setupEventListeners() {
    // Search and filters
    document.getElementById('search-news').addEventListener('input', debounce(loadNews, 300));
    document.getElementById('filter-status').addEventListener('change', loadNews);
    document.getElementById('filter-featured').addEventListener('change', loadNews);

    // Auto-generate slug from title
    document.getElementById('news-title').addEventListener('input', function() {
        const title = this.value;
        const slug = title.toLowerCase()
            .replace(/[^a-z0-9\s-]/g, '')
            .replace(/\s+/g, '-')
            .replace(/-+/g, '-')
            .trim('-');
        document.getElementById('news-slug').value = slug;
    });

    // Excerpt character counter
    document.getElementById('news-excerpt').addEventListener('input', function() {
        const count = this.value.length;
        document.getElementById('excerpt-count').textContent = count;

        if (count > 300) {
            this.classList.add('border-red-500');
        } else {
            this.classList.remove('border-red-500');
        }
    });

    // Form submission
    document.getElementById('news-form').addEventListener('submit', handleNewsSubmit);
}

function initNewsTinyMCE() {
    // Initialize TinyMCE for news content
    tinymce.init({
        selector: '#news-content',
        height: 400,
        menubar: true,
        plugins: [
            'advlist', 'autolink', 'lists', 'link', 'image', 'charmap', 'preview',
            'anchor', 'searchreplace', 'visualblocks', 'code', 'fullscreen',
            'insertdatetime', 'media', 'table', 'help', 'wordcount'
        ],
        toolbar: 'undo redo | blocks | ' +
                'bold italic backcolor | alignleft aligncenter ' +
                'alignright alignjustify | bullist numlist outdent indent | ' +
                'removeformat | image | code | help',
        content_style: 'body { font-family:Helvetica,Arial,sans-serif; font-size:14px }',
        branding: false,
        promotion: false,

        // Image upload handler
        images_upload_handler: async function (blobInfo, progress) {
            return new Promise((resolve, reject) => {
                const formData = new FormData();
                formData.append('file', blobInfo.blob(), blobInfo.filename());

                showLoading();

                fetch('/api/cms/media/upload', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('access_token')}`
                    },
                    body: formData
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Failed to upload image');
                    }
                    return response.json();
                })
                .then(data => {
                    const imageUrl = `/uploads/${data.filename}`;
                    resolve(imageUrl);
                    showToast('Image uploaded successfully', 'success');
                })
                .catch(error => {
                    reject(error.message || 'Image upload failed');
                    showToast('Image upload failed', 'error');
                })
                .finally(() => {
                    hideLoading();
                });
            });
        }
    });

    newsTinyMCEInstance = tinymce.get('news-content');
}

function destroyNewsTinyMCE() {
    if (tinymce.get('news-content')) {
        tinymce.get('news-content').remove();
    }
}

async function loadNews() {
    showLoading();

    const search = document.getElementById('search-news').value;
    const status = document.getElementById('filter-status').value;
    const featured = document.getElementById('filter-featured').value;

    try {
        // For now, we'll use mock data since the news API isn't implemented yet
        const mockNews = [
            {
                id: 1,
                title: "New Batch Admissions Open for 2024-25",
                slug: "new-batch-admissions-2024-25",
                excerpt: "Applications are now open for the new academic year 2024-25. Limited seats available.",
                content: "<p>We are pleased to announce that admissions are now open for the academic year 2024-25...</p>",
                is_published: true,
                is_featured: true,
                publish_date: "2024-01-15T10:00:00",
                created_at: "2024-01-15T10:00:00",
                author: { full_name: "Admin User" }
            },
            {
                id: 2,
                title: "Industrial Visit to Local Manufacturing Unit",
                slug: "industrial-visit-manufacturing-unit",
                excerpt: "Students visited ABC Manufacturing to gain practical exposure to industrial processes.",
                content: "<p>Our students had an excellent opportunity to visit ABC Manufacturing...</p>",
                is_published: true,
                is_featured: false,
                publish_date: "2024-01-10T14:30:00",
                created_at: "2024-01-10T14:30:00",
                author: { full_name: "Faculty Member" }
            }
        ];

        // Filter mock data based on search and filters
        let filteredNews = mockNews;

        if (search) {
            filteredNews = filteredNews.filter(article =>
                article.title.toLowerCase().includes(search.toLowerCase()) ||
                article.excerpt.toLowerCase().includes(search.toLowerCase())
            );
        }

        if (status) {
            filteredNews = filteredNews.filter(article =>
                status === 'published' ? article.is_published : !article.is_published
            );
        }

        if (featured) {
            filteredNews = filteredNews.filter(article =>
                featured === 'featured' ? article.is_featured : !article.is_featured
            );
        }

        displayNews(filteredNews);

    } catch (error) {
        console.error('Error loading news:', error);
        showToast('Error loading news articles', 'error');
    } finally {
        hideLoading();
    }
}

function displayNews(newsArticles) {
    const tbody = document.getElementById('news-table-body');

    if (newsArticles.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="5" class="px-6 py-12 text-center text-gray-500">
                    <i class="fas fa-newspaper text-4xl mb-4 text-gray-300"></i>
                    <p class="text-lg font-medium mb-2">No news articles found</p>
                    <p class="text-sm">Create your first news article to get started</p>
                </td>
            </tr>
        `;
        return;
    }

    tbody.innerHTML = newsArticles.map(article => `
        <tr class="hover:bg-gray-50">
            <td class="px-6 py-4">
                <div>
                    <div class="text-sm font-medium text-gray-900">${article.title}</div>
                    <div class="text-sm text-gray-500 mt-1">${article.excerpt || 'No excerpt'}</div>
                    ${article.is_featured ? '<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 mt-2">Featured</span>' : ''}
                </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${article.is_published ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}">
                    ${article.is_published ? 'Published' : 'Draft'}
                </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                ${article.author ? article.author.full_name : 'Unknown'}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                ${new Date(article.publish_date || article.created_at).toLocaleDateString()}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <div class="flex space-x-2">
                    <button onclick="editNews(${article.id})" class="text-blue-600 hover:text-blue-800" title="Edit">
                        <i class="fas fa-edit"></i>
                    </button>
                    <a href="/news/${article.slug}" target="_blank" class="text-green-600 hover:text-green-800" title="View">
                        <i class="fas fa-external-link-alt"></i>
                    </a>
                    <button onclick="deleteNews(${article.id}, '${article.title}')" class="text-red-600 hover:text-red-800" title="Delete">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
}

function openCreateNewsModal() {
    currentEditingNewsId = null;
    document.getElementById('news-modal-title').textContent = 'Create News Article';
    document.getElementById('news-save-button-text').textContent = 'Save Article';
    document.getElementById('news-form').reset();

    // Clear TinyMCE content
    if (tinymce.get('news-content')) {
        tinymce.get('news-content').setContent('');
    }

    // Set current date/time
    const now = new Date();
    now.setMinutes(now.getMinutes() - now.getTimezoneOffset());
    document.getElementById('news-publish-date').value = now.toISOString().slice(0, 16);

    document.getElementById('news-modal').classList.remove('hidden');

    // Re-initialize TinyMCE if needed
    setTimeout(() => {
        if (!tinymce.get('news-content')) {
            initNewsTinyMCE();
        }
    }, 100);
}

function closeNewsModal() {
    document.getElementById('news-modal').classList.add('hidden');
    currentEditingNewsId = null;
    destroyNewsTinyMCE();
}

function editNews(newsId) {
    // Mock data for editing - in real implementation, fetch from API
    const mockArticle = {
        id: newsId,
        title: "New Batch Admissions Open for 2024-25",
        slug: "new-batch-admissions-2024-25",
        excerpt: "Applications are now open for the new academic year 2024-25. Limited seats available.",
        content: "<p>We are pleased to announce that admissions are now open for the academic year 2024-25...</p>",
        is_published: true,
        is_featured: true,
        publish_date: "2024-01-15T10:00:00",
        featured_image: ""
    };

    currentEditingNewsId = newsId;
    document.getElementById('news-modal-title').textContent = 'Edit News Article';
    document.getElementById('news-save-button-text').textContent = 'Update Article';

    // Populate form
    document.getElementById('news-id').value = mockArticle.id;
    document.getElementById('news-title').value = mockArticle.title;
    document.getElementById('news-slug').value = mockArticle.slug;
    document.getElementById('news-excerpt').value = mockArticle.excerpt || '';
    document.getElementById('news-featured-image').value = mockArticle.featured_image || '';
    document.getElementById('news-publish-date').value = mockArticle.publish_date ? mockArticle.publish_date.slice(0, 16) : '';
    document.getElementById('news-published').checked = mockArticle.is_published;
    document.getElementById('news-featured').checked = mockArticle.is_featured;

    // Set TinyMCE content
    if (tinymce.get('news-content')) {
        tinymce.get('news-content').setContent(mockArticle.content || '');
    } else {
        document.getElementById('news-content').value = mockArticle.content || '';
    }

    // Update character counter
    const excerpt = document.getElementById('news-excerpt');
    document.getElementById('excerpt-count').textContent = excerpt.value.length;

    document.getElementById('news-modal').classList.remove('hidden');
}

async function handleNewsSubmit(e) {
    e.preventDefault();

    const formData = new FormData(e.target);

    // Get content from TinyMCE if available
    let content = formData.get('content');
    if (tinymce.get('news-content')) {
        content = tinymce.get('news-content').getContent();
    }

    const newsData = {
        title: formData.get('title'),
        slug: formData.get('slug'),
        excerpt: formData.get('excerpt'),
        content: content,
        featured_image: formData.get('featured_image'),
        publish_date: formData.get('publish_date'),
        is_published: formData.has('is_published'),
        is_featured: formData.has('is_featured')
    };

    try {
        showLoading();

        // Mock API call - replace with actual API when implemented
        console.log('News data to save:', newsData);

        showToast(currentEditingNewsId ? 'Article updated successfully' : 'Article created successfully', 'success');
        closeNewsModal();
        loadNews();

    } catch (error) {
        console.error('Error saving news article:', error);
        showToast(error.message, 'error');
    } finally {
        hideLoading();
    }
}

function deleteNews(newsId, newsTitle) {
    currentDeleteNewsId = newsId;
    document.getElementById('delete-news-title').textContent = newsTitle;
    document.getElementById('delete-news-modal').classList.remove('hidden');
}

function closeDeleteNewsModal() {
    document.getElementById('delete-news-modal').classList.add('hidden');
    currentDeleteNewsId = null;
}

async function confirmDeleteNews() {
    if (!currentDeleteNewsId) return;

    try {
        showLoading();

        // Mock API call - replace with actual API when implemented
        console.log('Deleting news article:', currentDeleteNewsId);

        showToast('Article deleted successfully', 'success');
        closeDeleteNewsModal();
        loadNews();

    } catch (error) {
        console.error('Error deleting news article:', error);
        showToast('Error deleting article', 'error');
    } finally {
        hideLoading();
    }
}

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}
</script>
{% endblock %}
