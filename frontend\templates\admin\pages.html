{% extends "admin/base.html" %}

{% block page_title %}Pages Management{% endblock %}
{% block page_description %}Manage website pages and content{% endblock %}

{% block extra_head %}
<!-- TinyMCE Rich Text Editor -->
<script src="https://cdn.tiny.cloud/1/no-api-key/tinymce/6/tinymce.min.js" referrerpolicy="origin"></script>
{% endblock %}

{% block content %}
<!-- Header Actions -->
<div class="flex justify-between items-center mb-6">
    <div>
        <h3 class="text-lg font-semibold text-gray-900">All Pages</h3>
        <p class="text-sm text-gray-600">Create, edit, and manage your website pages</p>
    </div>
    <button onclick="openCreatePageModal()" class="bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors">
        <i class="fas fa-plus mr-2"></i>New Page
    </button>
</div>

<!-- Filters and Search -->
<div class="bg-white rounded-lg shadow mb-6 p-4">
    <div class="flex flex-col md:flex-row gap-4">
        <div class="flex-1">
            <input 
                type="text" 
                id="search-pages" 
                placeholder="Search pages..." 
                class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
            >
        </div>
        <div class="flex gap-2">
            <select id="filter-status" class="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500">
                <option value="">All Status</option>
                <option value="published">Published</option>
                <option value="draft">Draft</option>
            </select>
            <select id="filter-template" class="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500">
                <option value="">All Templates</option>
                <option value="generic">Generic</option>
                <option value="home">Home</option>
                <option value="contact">Contact</option>
                <option value="gallery">Gallery</option>
            </select>
        </div>
    </div>
</div>

<!-- Pages Table -->
<div class="bg-white rounded-lg shadow overflow-hidden">
    <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Title
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Slug
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Template
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Author
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Updated
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                    </th>
                </tr>
            </thead>
            <tbody id="pages-table-body" class="bg-white divide-y divide-gray-200">
                <!-- Pages will be loaded here -->
                <tr>
                    <td colspan="7" class="px-6 py-12 text-center text-gray-500">
                        <i class="fas fa-spinner fa-spin text-2xl mb-2"></i>
                        <p>Loading pages...</p>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
</div>

<!-- Pagination -->
<div id="pagination" class="mt-6 flex items-center justify-between">
    <div class="text-sm text-gray-700">
        Showing <span id="showing-from">0</span> to <span id="showing-to">0</span> of <span id="total-pages">0</span> pages
    </div>
    <div class="flex space-x-2">
        <button id="prev-page" class="px-3 py-2 border border-gray-300 rounded-lg text-sm hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
            Previous
        </button>
        <button id="next-page" class="px-3 py-2 border border-gray-300 rounded-lg text-sm hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
            Next
        </button>
    </div>
</div>

<!-- Create/Edit Page Modal -->
<div id="page-modal" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
    <div class="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <div class="p-6 border-b border-gray-200">
            <div class="flex justify-between items-center">
                <h3 id="modal-title" class="text-lg font-semibold text-gray-900">Create New Page</h3>
                <button onclick="closePageModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
        </div>
        
        <form id="page-form" class="p-6 space-y-6">
            <input type="hidden" id="page-id" name="id">
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="page-title" class="block text-sm font-medium text-gray-700 mb-2">Title *</label>
                    <input 
                        type="text" 
                        id="page-title" 
                        name="title" 
                        required
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                        placeholder="Enter page title"
                    >
                </div>
                
                <div>
                    <label for="page-slug" class="block text-sm font-medium text-gray-700 mb-2">Slug *</label>
                    <input 
                        type="text" 
                        id="page-slug" 
                        name="slug" 
                        required
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                        placeholder="page-url-slug"
                    >
                    <p class="text-xs text-gray-500 mt-1">URL-friendly version of the title</p>
                </div>
            </div>
            
            <div>
                <label for="page-content" class="block text-sm font-medium text-gray-700 mb-2">Content</label>
                <textarea
                    id="page-content"
                    name="content"
                    rows="15"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                    placeholder="Enter page content..."
                ></textarea>
                <p class="text-xs text-gray-500 mt-1">Use the rich text editor to format your content with headings, lists, images, and more.</p>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="page-meta-description" class="block text-sm font-medium text-gray-700 mb-2">Meta Description</label>
                    <textarea 
                        id="page-meta-description" 
                        name="meta_description" 
                        rows="3"
                        maxlength="160"
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                        placeholder="Brief description for search engines (max 160 characters)"
                    ></textarea>
                    <p class="text-xs text-gray-500 mt-1"><span id="meta-desc-count">0</span>/160 characters</p>
                </div>
                
                <div>
                    <label for="page-meta-keywords" class="block text-sm font-medium text-gray-700 mb-2">Meta Keywords</label>
                    <input 
                        type="text" 
                        id="page-meta-keywords" 
                        name="meta_keywords"
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                        placeholder="keyword1, keyword2, keyword3"
                    >
                    <p class="text-xs text-gray-500 mt-1">Comma-separated keywords</p>
                </div>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                    <label for="page-template" class="block text-sm font-medium text-gray-700 mb-2">Template</label>
                    <select 
                        id="page-template" 
                        name="template"
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                    >
                        <option value="generic">Generic</option>
                        <option value="home">Home</option>
                        <option value="contact">Contact</option>
                        <option value="gallery">Gallery</option>
                    </select>
                </div>
                
                <div>
                    <label for="page-sort-order" class="block text-sm font-medium text-gray-700 mb-2">Sort Order</label>
                    <input 
                        type="number" 
                        id="page-sort-order" 
                        name="sort_order"
                        min="0"
                        value="0"
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                    >
                </div>
                
                <div class="flex items-center space-x-4 pt-6">
                    <label class="flex items-center">
                        <input 
                            type="checkbox" 
                            id="page-published" 
                            name="is_published"
                            class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                        >
                        <span class="ml-2 text-sm text-gray-700">Published</span>
                    </label>
                    
                    <label class="flex items-center">
                        <input 
                            type="checkbox" 
                            id="page-homepage" 
                            name="is_homepage"
                            class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                        >
                        <span class="ml-2 text-sm text-gray-700">Homepage</span>
                    </label>
                </div>
            </div>
            
            <div class="flex justify-end space-x-4 pt-6 border-t border-gray-200">
                <button type="button" onclick="closePageModal()" class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors">
                    Cancel
                </button>
                <button type="submit" class="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors">
                    <span id="save-button-text">Save Page</span>
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div id="delete-modal" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
    <div class="bg-white rounded-lg shadow-xl w-full max-w-md">
        <div class="p-6">
            <div class="flex items-center mb-4">
                <div class="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center mr-4">
                    <i class="fas fa-exclamation-triangle text-red-600"></i>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-900">Delete Page</h3>
                    <p class="text-sm text-gray-600">This action cannot be undone</p>
                </div>
            </div>
            
            <p class="text-gray-700 mb-6">Are you sure you want to delete "<span id="delete-page-title"></span>"?</p>
            
            <div class="flex justify-end space-x-4">
                <button onclick="closeDeleteModal()" class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors">
                    Cancel
                </button>
                <button onclick="confirmDeletePage()" class="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors">
                    Delete Page
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
let currentPage = 1;
let totalPages = 0;
let currentEditingPageId = null;
let currentDeletePageId = null;
let tinyMCEInstance = null;

document.addEventListener('DOMContentLoaded', function() {
    loadPages();
    setupEventListeners();
    initTinyMCE();
});

function setupEventListeners() {
    // Search and filters
    document.getElementById('search-pages').addEventListener('input', debounce(loadPages, 300));
    document.getElementById('filter-status').addEventListener('change', loadPages);
    document.getElementById('filter-template').addEventListener('change', loadPages);
    
    // Auto-generate slug from title
    document.getElementById('page-title').addEventListener('input', function() {
        const title = this.value;
        const slug = title.toLowerCase()
            .replace(/[^a-z0-9\s-]/g, '')
            .replace(/\s+/g, '-')
            .replace(/-+/g, '-')
            .trim('-');
        document.getElementById('page-slug').value = slug;
    });
    
    // Meta description character counter
    document.getElementById('page-meta-description').addEventListener('input', function() {
        const count = this.value.length;
        document.getElementById('meta-desc-count').textContent = count;
        
        if (count > 160) {
            this.classList.add('border-red-500');
        } else {
            this.classList.remove('border-red-500');
        }
    });
    
    // Form submission
    document.getElementById('page-form').addEventListener('submit', handlePageSubmit);
    
    // Pagination
    document.getElementById('prev-page').addEventListener('click', () => {
        if (currentPage > 1) {
            currentPage--;
            loadPages();
        }
    });
    
    document.getElementById('next-page').addEventListener('click', () => {
        if (currentPage < totalPages) {
            currentPage++;
            loadPages();
        }
    });
}

async function loadPages() {
    showLoading();
    
    const search = document.getElementById('search-pages').value;
    const status = document.getElementById('filter-status').value;
    const template = document.getElementById('filter-template').value;
    
    try {
        const response = await fetch('/api/cms/pages', {
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('access_token')}`
            }
        });
        
        if (response.ok) {
            const pages = await response.json();
            
            // Filter pages based on search and filters
            let filteredPages = pages;
            
            if (search) {
                filteredPages = filteredPages.filter(page => 
                    page.title.toLowerCase().includes(search.toLowerCase()) ||
                    page.slug.toLowerCase().includes(search.toLowerCase())
                );
            }
            
            if (status) {
                filteredPages = filteredPages.filter(page => 
                    status === 'published' ? page.is_published : !page.is_published
                );
            }
            
            if (template) {
                filteredPages = filteredPages.filter(page => page.template === template);
            }
            
            displayPages(filteredPages);
            updatePagination(filteredPages.length);
        } else {
            throw new Error('Failed to load pages');
        }
    } catch (error) {
        console.error('Error loading pages:', error);
        showToast('Error loading pages', 'error');
    } finally {
        hideLoading();
    }
}

function displayPages(pages) {
    const tbody = document.getElementById('pages-table-body');
    
    if (pages.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="7" class="px-6 py-12 text-center text-gray-500">
                    <i class="fas fa-file-alt text-4xl mb-4 text-gray-300"></i>
                    <p class="text-lg font-medium mb-2">No pages found</p>
                    <p class="text-sm">Create your first page to get started</p>
                </td>
            </tr>
        `;
        return;
    }
    
    tbody.innerHTML = pages.map(page => `
        <tr class="hover:bg-gray-50">
            <td class="px-6 py-4 whitespace-nowrap">
                <div>
                    <div class="text-sm font-medium text-gray-900">${page.title}</div>
                    ${page.is_homepage ? '<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 mt-1">Homepage</span>' : ''}
                </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900 font-mono">/${page.slug}</div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${page.is_published ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}">
                    ${page.is_published ? 'Published' : 'Draft'}
                </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 capitalize">
                ${page.template}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                ${page.author ? page.author.full_name : 'Unknown'}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                ${new Date(page.updated_at || page.created_at).toLocaleDateString()}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <div class="flex space-x-2">
                    <button onclick="editPage(${page.id})" class="text-blue-600 hover:text-blue-800" title="Edit">
                        <i class="fas fa-edit"></i>
                    </button>
                    <a href="/${page.slug}" target="_blank" class="text-green-600 hover:text-green-800" title="View">
                        <i class="fas fa-external-link-alt"></i>
                    </a>
                    <button onclick="deletePage(${page.id}, '${page.title}')" class="text-red-600 hover:text-red-800" title="Delete">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
}

function updatePagination(total) {
    document.getElementById('total-pages').textContent = total;
    document.getElementById('showing-from').textContent = total > 0 ? 1 : 0;
    document.getElementById('showing-to').textContent = total;
}

function openCreatePageModal() {
    currentEditingPageId = null;
    document.getElementById('modal-title').textContent = 'Create New Page';
    document.getElementById('save-button-text').textContent = 'Save Page';
    document.getElementById('page-form').reset();

    // Clear TinyMCE content
    if (tinymce.get('page-content')) {
        tinymce.get('page-content').setContent('');
    }

    document.getElementById('page-modal').classList.remove('hidden');

    // Re-initialize TinyMCE if needed
    setTimeout(() => {
        if (!tinymce.get('page-content')) {
            initTinyMCE();
        }
    }, 100);
}

function closePageModal() {
    document.getElementById('page-modal').classList.add('hidden');
    currentEditingPageId = null;

    // Clean up TinyMCE to prevent memory leaks
    destroyTinyMCE();
}

async function editPage(pageId) {
    try {
        const response = await fetch(`/api/cms/pages/${pageId}`, {
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('access_token')}`
            }
        });
        
        if (response.ok) {
            const page = await response.json();
            
            currentEditingPageId = pageId;
            document.getElementById('modal-title').textContent = 'Edit Page';
            document.getElementById('save-button-text').textContent = 'Update Page';
            
            // Populate form
            document.getElementById('page-id').value = page.id;
            document.getElementById('page-title').value = page.title;
            document.getElementById('page-slug').value = page.slug;
            document.getElementById('page-meta-description').value = page.meta_description || '';
            document.getElementById('page-meta-keywords').value = page.meta_keywords || '';
            document.getElementById('page-template').value = page.template;
            document.getElementById('page-sort-order').value = page.sort_order;
            document.getElementById('page-published').checked = page.is_published;
            document.getElementById('page-homepage').checked = page.is_homepage;

            // Set TinyMCE content
            if (tinymce.get('page-content')) {
                tinymce.get('page-content').setContent(page.content || '');
            } else {
                document.getElementById('page-content').value = page.content || '';
            }
            
            // Update character counter
            const metaDesc = document.getElementById('page-meta-description');
            document.getElementById('meta-desc-count').textContent = metaDesc.value.length;
            
            document.getElementById('page-modal').classList.remove('hidden');
        } else {
            throw new Error('Failed to load page');
        }
    } catch (error) {
        console.error('Error loading page:', error);
        showToast('Error loading page', 'error');
    }
}

async function handlePageSubmit(e) {
    e.preventDefault();

    const formData = new FormData(e.target);

    // Get content from TinyMCE if available, otherwise from textarea
    let content = formData.get('content');
    if (tinymce.get('page-content')) {
        content = tinymce.get('page-content').getContent();
    }

    const pageData = {
        title: formData.get('title'),
        slug: formData.get('slug'),
        content: content,
        meta_description: formData.get('meta_description'),
        meta_keywords: formData.get('meta_keywords'),
        template: formData.get('template'),
        sort_order: parseInt(formData.get('sort_order')) || 0,
        is_published: formData.has('is_published'),
        is_homepage: formData.has('is_homepage')
    };
    
    try {
        showLoading();
        
        const url = currentEditingPageId 
            ? `/api/cms/pages/${currentEditingPageId}`
            : '/api/cms/pages';
        
        const method = currentEditingPageId ? 'PUT' : 'POST';
        
        const response = await fetch(url, {
            method: method,
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(pageData)
        });
        
        if (response.ok) {
            showToast(currentEditingPageId ? 'Page updated successfully' : 'Page created successfully', 'success');
            closePageModal();
            loadPages();
        } else {
            const error = await response.json();
            throw new Error(error.detail || 'Failed to save page');
        }
    } catch (error) {
        console.error('Error saving page:', error);
        showToast(error.message, 'error');
    } finally {
        hideLoading();
    }
}

function deletePage(pageId, pageTitle) {
    currentDeletePageId = pageId;
    document.getElementById('delete-page-title').textContent = pageTitle;
    document.getElementById('delete-modal').classList.remove('hidden');
}

function closeDeleteModal() {
    document.getElementById('delete-modal').classList.add('hidden');
    currentDeletePageId = null;
}

async function confirmDeletePage() {
    if (!currentDeletePageId) return;
    
    try {
        showLoading();
        
        const response = await fetch(`/api/cms/pages/${currentDeletePageId}`, {
            method: 'DELETE',
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('access_token')}`
            }
        });
        
        if (response.ok) {
            showToast('Page deleted successfully', 'success');
            closeDeleteModal();
            loadPages();
        } else {
            throw new Error('Failed to delete page');
        }
    } catch (error) {
        console.error('Error deleting page:', error);
        showToast('Error deleting page', 'error');
    } finally {
        hideLoading();
    }
}

function initTinyMCE() {
    // Initialize TinyMCE
    tinymce.init({
        selector: '#page-content',
        height: 500,
        menubar: true,
        plugins: [
            'advlist', 'autolink', 'lists', 'link', 'image', 'charmap', 'preview',
            'anchor', 'searchreplace', 'visualblocks', 'code', 'fullscreen',
            'insertdatetime', 'media', 'table', 'help', 'wordcount'
        ],
        toolbar: 'undo redo | blocks | ' +
                'bold italic backcolor | alignleft aligncenter ' +
                'alignright alignjustify | bullist numlist outdent indent | ' +
                'removeformat | image | code | help',
        content_style: 'body { font-family:Helvetica,Arial,sans-serif; font-size:14px }',
        branding: false,
        promotion: false,

        // Image upload handler
        images_upload_handler: async function (blobInfo, progress) {
            return new Promise((resolve, reject) => {
                const formData = new FormData();
                formData.append('file', blobInfo.blob(), blobInfo.filename());

                // Show loading indicator
                showLoading();

                fetch('/api/cms/media/upload', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('access_token')}`
                    },
                    body: formData
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Failed to upload image');
                    }
                    return response.json();
                })
                .then(data => {
                    // Resolve with the image URL
                    const imageUrl = `/uploads/${data.filename}`;
                    resolve(imageUrl);
                    showToast('Image uploaded successfully', 'success');
                })
                .catch(error => {
                    reject(error.message || 'Image upload failed');
                    showToast('Image upload failed', 'error');
                })
                .finally(() => {
                    hideLoading();
                });
            });
        }
    });

    // Store the instance for later use
    tinyMCEInstance = tinymce.get('page-content');
}

// Clean up TinyMCE when closing modal
function destroyTinyMCE() {
    if (tinymce.get('page-content')) {
        tinymce.get('page-content').remove();
    }
}

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}
</script>
{% endblock %}
