{% extends "admin/base.html" %}

{% block page_title %}Event Calendar{% endblock %}
{% block page_description %}Manage institute events and calendar{% endblock %}

{% block extra_head %}
<!-- FullCalendar CSS -->
<link href="https://cdn.jsdelivr.net/npm/fullcalendar@6.1.10/index.global.min.css" rel="stylesheet">
<!-- TinyMCE Rich Text Editor -->
<script src="https://cdn.tiny.cloud/1/no-api-key/tinymce/6/tinymce.min.js" referrerpolicy="origin"></script>
{% endblock %}

{% block content %}
<!-- Header Actions -->
<div class="flex justify-between items-center mb-6">
    <div>
        <h3 class="text-lg font-semibold text-gray-900">Event Calendar</h3>
        <p class="text-sm text-gray-600">Manage institute events, holidays, and important dates</p>
    </div>
    <div class="flex space-x-2">
        <button onclick="openCreateEventModal()" class="bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors">
            <i class="fas fa-plus mr-2"></i>New Event
        </button>
        <button onclick="toggleCalendarView()" id="view-toggle" class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors">
            <i class="fas fa-list mr-2"></i>List View
        </button>
    </div>
</div>

<!-- Calendar View -->
<div id="calendar-view" class="bg-white rounded-lg shadow p-6 mb-6">
    <div id="calendar"></div>
</div>

<!-- List View -->
<div id="list-view" class="hidden">
    <!-- Filters -->
    <div class="bg-white rounded-lg shadow mb-6 p-4">
        <div class="flex flex-col md:flex-row gap-4">
            <div class="flex-1">
                <input 
                    type="text" 
                    id="search-events" 
                    placeholder="Search events..." 
                    class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                >
            </div>
            <div class="flex gap-2">
                <select id="filter-type" class="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500">
                    <option value="">All Types</option>
                    <option value="academic">Academic</option>
                    <option value="holiday">Holiday</option>
                    <option value="exam">Examination</option>
                    <option value="cultural">Cultural</option>
                    <option value="sports">Sports</option>
                    <option value="other">Other</option>
                </select>
                <select id="filter-status" class="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500">
                    <option value="">All Events</option>
                    <option value="upcoming">Upcoming</option>
                    <option value="ongoing">Ongoing</option>
                    <option value="completed">Completed</option>
                </select>
            </div>
        </div>
    </div>

    <!-- Events Table -->
    <div class="bg-white rounded-lg shadow overflow-hidden">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Event
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Type
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Date & Time
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Location
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Status
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Actions
                        </th>
                    </tr>
                </thead>
                <tbody id="events-table-body" class="bg-white divide-y divide-gray-200">
                    <!-- Events will be loaded here -->
                    <tr>
                        <td colspan="6" class="px-6 py-12 text-center text-gray-500">
                            <i class="fas fa-spinner fa-spin text-2xl mb-2"></i>
                            <p>Loading events...</p>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Create/Edit Event Modal -->
<div id="event-modal" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
    <div class="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <div class="p-6 border-b border-gray-200">
            <div class="flex justify-between items-center">
                <h3 id="event-modal-title" class="text-lg font-semibold text-gray-900">Create Event</h3>
                <button onclick="closeEventModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
        </div>
        
        <form id="event-form" class="p-6 space-y-6">
            <input type="hidden" id="event-id" name="id">
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="event-title" class="block text-sm font-medium text-gray-700 mb-2">Event Title *</label>
                    <input 
                        type="text" 
                        id="event-title" 
                        name="title" 
                        required
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                        placeholder="Enter event title"
                    >
                </div>
                
                <div>
                    <label for="event-type" class="block text-sm font-medium text-gray-700 mb-2">Event Type *</label>
                    <select 
                        id="event-type" 
                        name="type" 
                        required
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                    >
                        <option value="">Select type</option>
                        <option value="academic">Academic</option>
                        <option value="holiday">Holiday</option>
                        <option value="exam">Examination</option>
                        <option value="cultural">Cultural</option>
                        <option value="sports">Sports</option>
                        <option value="other">Other</option>
                    </select>
                </div>
            </div>
            
            <div>
                <label for="event-description" class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                <textarea 
                    id="event-description" 
                    name="description" 
                    rows="4"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                    placeholder="Enter event description..."
                ></textarea>
                <p class="text-xs text-gray-500 mt-1">Use the rich text editor to format your description.</p>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="event-start-date" class="block text-sm font-medium text-gray-700 mb-2">Start Date & Time *</label>
                    <input 
                        type="datetime-local" 
                        id="event-start-date" 
                        name="start_date" 
                        required
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                    >
                </div>
                
                <div>
                    <label for="event-end-date" class="block text-sm font-medium text-gray-700 mb-2">End Date & Time</label>
                    <input 
                        type="datetime-local" 
                        id="event-end-date" 
                        name="end_date"
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                    >
                    <p class="text-xs text-gray-500 mt-1">Leave empty for single-day events</p>
                </div>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="event-location" class="block text-sm font-medium text-gray-700 mb-2">Location</label>
                    <input 
                        type="text" 
                        id="event-location" 
                        name="location"
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                        placeholder="Event location or venue"
                    >
                </div>
                
                <div>
                    <label for="event-organizer" class="block text-sm font-medium text-gray-700 mb-2">Organizer</label>
                    <input 
                        type="text" 
                        id="event-organizer" 
                        name="organizer"
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                        placeholder="Event organizer or department"
                    >
                </div>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="event-color" class="block text-sm font-medium text-gray-700 mb-2">Calendar Color</label>
                    <select 
                        id="event-color" 
                        name="color"
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                    >
                        <option value="#3b82f6">Blue (Default)</option>
                        <option value="#ef4444">Red (Important)</option>
                        <option value="#10b981">Green (Academic)</option>
                        <option value="#f59e0b">Orange (Cultural)</option>
                        <option value="#8b5cf6">Purple (Sports)</option>
                        <option value="#6b7280">Gray (Holiday)</option>
                    </select>
                </div>
                
                <div class="flex items-center space-x-6 pt-6">
                    <label class="flex items-center">
                        <input 
                            type="checkbox" 
                            id="event-all-day" 
                            name="is_all_day"
                            class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                        >
                        <span class="ml-2 text-sm text-gray-700">All Day Event</span>
                    </label>
                    
                    <label class="flex items-center">
                        <input 
                            type="checkbox" 
                            id="event-public" 
                            name="is_public"
                            checked
                            class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                        >
                        <span class="ml-2 text-sm text-gray-700">Public Event</span>
                    </label>
                </div>
            </div>
            
            <div class="flex justify-end space-x-4 pt-6 border-t border-gray-200">
                <button type="button" onclick="closeEventModal()" class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors">
                    Cancel
                </button>
                <button type="submit" class="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors">
                    <span id="event-save-button-text">Save Event</span>
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div id="delete-event-modal" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
    <div class="bg-white rounded-lg shadow-xl w-full max-w-md">
        <div class="p-6">
            <div class="flex items-center mb-4">
                <div class="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center mr-4">
                    <i class="fas fa-exclamation-triangle text-red-600"></i>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-900">Delete Event</h3>
                    <p class="text-sm text-gray-600">This action cannot be undone</p>
                </div>
            </div>
            
            <p class="text-gray-700 mb-6">Are you sure you want to delete "<span id="delete-event-title"></span>"?</p>
            
            <div class="flex justify-end space-x-4">
                <button onclick="closeDeleteEventModal()" class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors">
                    Cancel
                </button>
                <button onclick="confirmDeleteEvent()" class="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors">
                    Delete Event
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<!-- FullCalendar JS -->
<script src="https://cdn.jsdelivr.net/npm/fullcalendar@6.1.10/index.global.min.js"></script>

<script>
let calendar;
let currentView = 'calendar';
let currentEditingEventId = null;
let currentDeleteEventId = null;
let eventTinyMCEInstance = null;

document.addEventListener('DOMContentLoaded', function() {
    initCalendar();
    loadEvents();
    setupEventListeners();
    initEventTinyMCE();
});

function setupEventListeners() {
    // Search and filters for list view
    document.getElementById('search-events').addEventListener('input', debounce(loadEventsList, 300));
    document.getElementById('filter-type').addEventListener('change', loadEventsList);
    document.getElementById('filter-status').addEventListener('change', loadEventsList);

    // Form submission
    document.getElementById('event-form').addEventListener('submit', handleEventSubmit);

    // All day checkbox handler
    document.getElementById('event-all-day').addEventListener('change', function() {
        const startDate = document.getElementById('event-start-date');
        const endDate = document.getElementById('event-end-date');

        if (this.checked) {
            // Convert to date-only inputs
            startDate.type = 'date';
            endDate.type = 'date';
        } else {
            // Convert to datetime inputs
            startDate.type = 'datetime-local';
            endDate.type = 'datetime-local';
        }
    });
}

function initCalendar() {
    const calendarEl = document.getElementById('calendar');

    calendar = new FullCalendar.Calendar(calendarEl, {
        initialView: 'dayGridMonth',
        headerToolbar: {
            left: 'prev,next today',
            center: 'title',
            right: 'dayGridMonth,timeGridWeek,timeGridDay'
        },
        editable: true,
        selectable: true,
        selectMirror: true,
        dayMaxEvents: true,
        weekends: true,

        // Event sources
        events: function(fetchInfo, successCallback, failureCallback) {
            // Load events from our data source
            getCalendarEvents().then(events => {
                successCallback(events);
            }).catch(error => {
                console.error('Error loading calendar events:', error);
                failureCallback(error);
            });
        },

        // Event handlers
        select: function(arg) {
            // Open create event modal with selected date
            openCreateEventModal(arg.start, arg.end);
            calendar.unselect();
        },

        eventClick: function(arg) {
            // Open edit event modal
            editEvent(arg.event.id);
        },

        eventDrop: function(arg) {
            // Handle event drag and drop
            updateEventDates(arg.event.id, arg.event.start, arg.event.end);
        },

        eventResize: function(arg) {
            // Handle event resize
            updateEventDates(arg.event.id, arg.event.start, arg.event.end);
        }
    });

    calendar.render();
}

function initEventTinyMCE() {
    // Initialize TinyMCE for event description
    tinymce.init({
        selector: '#event-description',
        height: 200,
        menubar: false,
        plugins: [
            'advlist', 'autolink', 'lists', 'link', 'charmap',
            'searchreplace', 'visualblocks', 'code', 'insertdatetime'
        ],
        toolbar: 'undo redo | blocks | bold italic | alignleft aligncenter alignright | bullist numlist | link | code',
        content_style: 'body { font-family:Helvetica,Arial,sans-serif; font-size:14px }',
        branding: false,
        promotion: false
    });

    eventTinyMCEInstance = tinymce.get('event-description');
}

function destroyEventTinyMCE() {
    if (tinymce.get('event-description')) {
        tinymce.get('event-description').remove();
    }
}

async function getCalendarEvents() {
    // Mock events data - replace with actual API call
    const mockEvents = [
        {
            id: '1',
            title: 'New Academic Year Begins',
            start: '2024-07-01',
            end: '2024-07-01',
            color: '#10b981',
            extendedProps: {
                type: 'academic',
                location: 'Main Campus',
                organizer: 'Administration',
                description: 'Welcome ceremony for new students'
            }
        },
        {
            id: '2',
            title: 'Independence Day Celebration',
            start: '2024-08-15',
            color: '#ef4444',
            extendedProps: {
                type: 'holiday',
                location: 'Main Ground',
                organizer: 'Cultural Committee',
                description: 'Flag hoisting and cultural programs'
            }
        },
        {
            id: '3',
            title: 'Mid-term Examinations',
            start: '2024-09-15',
            end: '2024-09-25',
            color: '#f59e0b',
            extendedProps: {
                type: 'exam',
                location: 'Examination Hall',
                organizer: 'Examination Department',
                description: 'Mid-term examinations for all trades'
            }
        },
        {
            id: '4',
            title: 'Annual Sports Day',
            start: '2024-10-02',
            color: '#8b5cf6',
            extendedProps: {
                type: 'sports',
                location: 'Sports Ground',
                organizer: 'Sports Department',
                description: 'Annual sports competition and awards'
            }
        },
        {
            id: '5',
            title: 'Industrial Visit',
            start: '2024-11-10T09:00:00',
            end: '2024-11-10T17:00:00',
            color: '#3b82f6',
            extendedProps: {
                type: 'academic',
                location: 'ABC Manufacturing Ltd.',
                organizer: 'Training Department',
                description: 'Educational visit to manufacturing facility'
            }
        }
    ];

    return mockEvents;
}

async function loadEvents() {
    // Refresh calendar events
    if (calendar) {
        calendar.refetchEvents();
    }

    // Load events for list view
    if (currentView === 'list') {
        loadEventsList();
    }
}

async function loadEventsList() {
    showLoading();

    const search = document.getElementById('search-events').value;
    const type = document.getElementById('filter-type').value;
    const status = document.getElementById('filter-status').value;

    try {
        const events = await getCalendarEvents();

        // Filter events
        let filteredEvents = events;

        if (search) {
            filteredEvents = filteredEvents.filter(event =>
                event.title.toLowerCase().includes(search.toLowerCase()) ||
                (event.extendedProps.description && event.extendedProps.description.toLowerCase().includes(search.toLowerCase()))
            );
        }

        if (type) {
            filteredEvents = filteredEvents.filter(event => event.extendedProps.type === type);
        }

        if (status) {
            const now = new Date();
            filteredEvents = filteredEvents.filter(event => {
                const eventStart = new Date(event.start);
                const eventEnd = event.end ? new Date(event.end) : eventStart;

                switch (status) {
                    case 'upcoming':
                        return eventStart > now;
                    case 'ongoing':
                        return eventStart <= now && eventEnd >= now;
                    case 'completed':
                        return eventEnd < now;
                    default:
                        return true;
                }
            });
        }

        displayEventsList(filteredEvents);

    } catch (error) {
        console.error('Error loading events list:', error);
        showToast('Error loading events', 'error');
    } finally {
        hideLoading();
    }
}

function displayEventsList(events) {
    const tbody = document.getElementById('events-table-body');

    if (events.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="6" class="px-6 py-12 text-center text-gray-500">
                    <i class="fas fa-calendar text-4xl mb-4 text-gray-300"></i>
                    <p class="text-lg font-medium mb-2">No events found</p>
                    <p class="text-sm">Create your first event to get started</p>
                </td>
            </tr>
        `;
        return;
    }

    tbody.innerHTML = events.map(event => {
        const startDate = new Date(event.start);
        const endDate = event.end ? new Date(event.end) : null;
        const now = new Date();

        let status = 'upcoming';
        let statusClass = 'bg-blue-100 text-blue-800';

        if (endDate && startDate <= now && endDate >= now) {
            status = 'ongoing';
            statusClass = 'bg-green-100 text-green-800';
        } else if ((endDate && endDate < now) || (!endDate && startDate < now)) {
            status = 'completed';
            statusClass = 'bg-gray-100 text-gray-800';
        }

        return `
            <tr class="hover:bg-gray-50">
                <td class="px-6 py-4">
                    <div>
                        <div class="text-sm font-medium text-gray-900">${event.title}</div>
                        <div class="text-sm text-gray-500 mt-1">${event.extendedProps.description || 'No description'}</div>
                    </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getTypeBadgeClass(event.extendedProps.type)}">
                        ${event.extendedProps.type.charAt(0).toUpperCase() + event.extendedProps.type.slice(1)}
                    </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <div>${startDate.toLocaleDateString()}</div>
                    ${endDate && endDate.toDateString() !== startDate.toDateString() ?
                        `<div class="text-xs text-gray-500">to ${endDate.toLocaleDateString()}</div>` :
                        `<div class="text-xs text-gray-500">${startDate.toLocaleTimeString()}</div>`
                    }
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    ${event.extendedProps.location || 'Not specified'}
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${statusClass}">
                        ${status.charAt(0).toUpperCase() + status.slice(1)}
                    </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div class="flex space-x-2">
                        <button onclick="editEvent('${event.id}')" class="text-blue-600 hover:text-blue-800" title="Edit">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button onclick="deleteEvent('${event.id}', '${event.title}')" class="text-red-600 hover:text-red-800" title="Delete">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }).join('');
}

function getTypeBadgeClass(type) {
    switch (type) {
        case 'academic':
            return 'bg-green-100 text-green-800';
        case 'holiday':
            return 'bg-red-100 text-red-800';
        case 'exam':
            return 'bg-yellow-100 text-yellow-800';
        case 'cultural':
            return 'bg-purple-100 text-purple-800';
        case 'sports':
            return 'bg-blue-100 text-blue-800';
        default:
            return 'bg-gray-100 text-gray-800';
    }
}

function toggleCalendarView() {
    const calendarView = document.getElementById('calendar-view');
    const listView = document.getElementById('list-view');
    const toggleButton = document.getElementById('view-toggle');

    if (currentView === 'calendar') {
        currentView = 'list';
        calendarView.classList.add('hidden');
        listView.classList.remove('hidden');
        toggleButton.innerHTML = '<i class="fas fa-calendar mr-2"></i>Calendar View';
        loadEventsList();
    } else {
        currentView = 'calendar';
        listView.classList.add('hidden');
        calendarView.classList.remove('hidden');
        toggleButton.innerHTML = '<i class="fas fa-list mr-2"></i>List View';
        if (calendar) {
            calendar.render();
        }
    }
}

function openCreateEventModal(startDate = null, endDate = null) {
    currentEditingEventId = null;
    document.getElementById('event-modal-title').textContent = 'Create Event';
    document.getElementById('event-save-button-text').textContent = 'Save Event';
    document.getElementById('event-form').reset();

    // Set default dates if provided
    if (startDate) {
        const start = new Date(startDate);
        start.setMinutes(start.getMinutes() - start.getTimezoneOffset());
        document.getElementById('event-start-date').value = start.toISOString().slice(0, 16);
    }

    if (endDate && endDate.toDateString() !== startDate.toDateString()) {
        const end = new Date(endDate);
        end.setMinutes(end.getMinutes() - end.getTimezoneOffset());
        document.getElementById('event-end-date').value = end.toISOString().slice(0, 16);
    }

    // Clear TinyMCE content
    if (tinymce.get('event-description')) {
        tinymce.get('event-description').setContent('');
    }

    document.getElementById('event-modal').classList.remove('hidden');

    // Re-initialize TinyMCE if needed
    setTimeout(() => {
        if (!tinymce.get('event-description')) {
            initEventTinyMCE();
        }
    }, 100);
}

function closeEventModal() {
    document.getElementById('event-modal').classList.add('hidden');
    currentEditingEventId = null;
    destroyEventTinyMCE();
}

async function editEvent(eventId) {
    try {
        const events = await getCalendarEvents();
        const event = events.find(e => e.id === eventId);

        if (event) {
            currentEditingEventId = eventId;
            document.getElementById('event-modal-title').textContent = 'Edit Event';
            document.getElementById('event-save-button-text').textContent = 'Update Event';

            // Populate form
            document.getElementById('event-id').value = event.id;
            document.getElementById('event-title').value = event.title;
            document.getElementById('event-type').value = event.extendedProps.type;
            document.getElementById('event-location').value = event.extendedProps.location || '';
            document.getElementById('event-organizer').value = event.extendedProps.organizer || '';
            document.getElementById('event-color').value = event.color || '#3b82f6';

            // Set dates
            const startDate = new Date(event.start);
            startDate.setMinutes(startDate.getMinutes() - startDate.getTimezoneOffset());
            document.getElementById('event-start-date').value = startDate.toISOString().slice(0, 16);

            if (event.end) {
                const endDate = new Date(event.end);
                endDate.setMinutes(endDate.getMinutes() - endDate.getTimezoneOffset());
                document.getElementById('event-end-date').value = endDate.toISOString().slice(0, 16);
            }

            // Set checkboxes
            document.getElementById('event-all-day').checked = event.allDay || false;
            document.getElementById('event-public').checked = event.extendedProps.is_public !== false;

            // Set TinyMCE content
            if (tinymce.get('event-description')) {
                tinymce.get('event-description').setContent(event.extendedProps.description || '');
            } else {
                document.getElementById('event-description').value = event.extendedProps.description || '';
            }

            document.getElementById('event-modal').classList.remove('hidden');
        }
    } catch (error) {
        console.error('Error loading event:', error);
        showToast('Error loading event', 'error');
    }
}

async function handleEventSubmit(e) {
    e.preventDefault();

    const formData = new FormData(e.target);

    // Get description from TinyMCE if available
    let description = formData.get('description');
    if (tinymce.get('event-description')) {
        description = tinymce.get('event-description').getContent();
    }

    const eventData = {
        title: formData.get('title'),
        type: formData.get('type'),
        description: description,
        start_date: formData.get('start_date'),
        end_date: formData.get('end_date'),
        location: formData.get('location'),
        organizer: formData.get('organizer'),
        color: formData.get('color'),
        is_all_day: formData.has('is_all_day'),
        is_public: formData.has('is_public')
    };

    try {
        showLoading();

        // Mock API call - replace with actual API when implemented
        console.log('Event data to save:', eventData);

        showToast(currentEditingEventId ? 'Event updated successfully' : 'Event created successfully', 'success');
        closeEventModal();
        loadEvents();

    } catch (error) {
        console.error('Error saving event:', error);
        showToast(error.message, 'error');
    } finally {
        hideLoading();
    }
}

async function updateEventDates(eventId, startDate, endDate) {
    try {
        // Mock API call to update event dates
        console.log('Updating event dates:', { eventId, startDate, endDate });
        showToast('Event updated successfully', 'success');
    } catch (error) {
        console.error('Error updating event:', error);
        showToast('Error updating event', 'error');
        // Revert the calendar change
        calendar.refetchEvents();
    }
}

function deleteEvent(eventId, eventTitle) {
    currentDeleteEventId = eventId;
    document.getElementById('delete-event-title').textContent = eventTitle;
    document.getElementById('delete-event-modal').classList.remove('hidden');
}

function closeDeleteEventModal() {
    document.getElementById('delete-event-modal').classList.add('hidden');
    currentDeleteEventId = null;
}

async function confirmDeleteEvent() {
    if (!currentDeleteEventId) return;

    try {
        showLoading();

        // Mock API call - replace with actual API when implemented
        console.log('Deleting event:', currentDeleteEventId);

        showToast('Event deleted successfully', 'success');
        closeDeleteEventModal();
        loadEvents();

    } catch (error) {
        console.error('Error deleting event:', error);
        showToast('Error deleting event', 'error');
    } finally {
        hideLoading();
    }
}

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}
</script>
{% endblock %}
